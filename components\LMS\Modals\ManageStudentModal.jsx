import { InputSwitch } from 'primereact/inputswitch'
import TextInput from '../../UI/Input/TextInput/TextInput'
import style from '../../../pages/LMS/index.module.css'
import clsx from 'clsx'
import Button from '../../UI/Button/Button'
import SelectInput from '../../UI/Input/SelectInput/SelectInput'
import { ConditionalDisplay } from '../../UI/ConditionalDisplay/ConditionalDisplay'
import Success from '../../../svg/metronic/success.svg'
import Close from '../../../svg/metronic/close_toast.svg'
import Image from 'next/image'

export default function ManageStudentModal({
  course,
  setCourse,
  updateCourse,
  toast,
  loading,
  setShowManageStudentsModal,
}) {
  const students = [{ label: 'Erica <PERSON>guera', value: 29 }]
  const handleChange = (key, value) => {
    console.log('key', key, value)
    setCourse((prev) => ({ ...prev, [key]: value }))
  }

  const handleUpdate = () => {
    const requestBody = {
      courseId: course?.id,
      title: course?.title,
      overview: course?.overview,
      courseTypeId: course?.courseTypeId,
      thumbnailType: course?.thumbnailType,
      thumbnailName: course?.thumbnailName,
      thumbnailSize: course?.thumbnailSize,
      hasPublished: course?.hasPublished,
      availableToAll: course?.availableToAll,
      availableToLeadIds: course?.availableToLeadUserProfileIds,
    }

    updateCourse(requestBody, () => {
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: `Updated Successfully.`,
        icon: <Image src={Success} alt="success" />,
        closeIcon: <Image src={Close} alt="close" />,
      })
      setShowManageStudentsModal(false)
    })
  }

  return (
    <>
      <div className="flex flex-column gap-4">
        {/* <TextInput theme="metronic" label="Course Name" /> */}
        <div className="flex flex-column gap-2">
          <span className={clsx('font-semibold', style.fontText)}>
            Available to All
          </span>
          <InputSwitch
            checked={course?.availableToAll}
            className="custom-lead"
            onChange={(e) => handleChange('availableToAll', e.value)}
          />
        </div>

        <ConditionalDisplay condition={!course?.availableToAll}>
          <SelectInput
            theme="metronic"
            label="Add Student"
            placeholder="Add Student Name"
            options={students}
            value={course?.availableToLeadUserProfileIds}
            optionLabel="label"
            optionValue="value"
            onChange={(e) =>
              handleChange('availableToLeadUserProfileIds', e.value)
            }
            multiple={true}
          />

          <div className="relative w-full flex align-items-center">
            <TextInput theme="metronic" label="Added Student" />
            <ConditionalDisplay
              condition={course?.availableToLeadUserProfileIds}
            >
              {course?.availableToLeadUserProfileIds?.map((student, index) => (
                <div
                  key={index}
                  className={clsx(
                    'absolute top-1/2 transform -translate-y-1/2 cursor-pointer mt-4 -mb-1 ml-2',
                    style.uploadedBox
                  )}
                >
                  <span className="ml-2" style={{ flex: 1 }}>
                    {students.find((s) => s.value === student)?.label}
                  </span>
                  <i
                    className={clsx(
                      'pi pi-times text-xs font-bold',
                      style.fontText
                    )}
                  ></i>
                </div>
              ))}
            </ConditionalDisplay>
          </div>
        </ConditionalDisplay>
      </div>
      <div className="flex gap-3 justify-content-end my-5">
        <Button
          theme="metronic"
          variant="outline"
          label="Cancel"
          width="150px"
        />
        <Button
          theme="metronic"
          label="Update"
          width="150px"
          onClick={handleUpdate}
          loading={loading}
        />
      </div>
    </>
  )
}
