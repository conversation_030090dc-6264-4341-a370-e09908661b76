import Image from 'next/image'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import CourseCard from '../../../components/LMS/CourseCard'
import PendingCard from '../../../components/LMS/PendingCard'
import style from '../index.module.css'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import clsx from 'clsx'
import { useContext } from 'react'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import emptyCard from '../../../svg/metronic/empty_course_card.svg'
import { useRouter } from 'next/router'
import { useEnrolledCoursesData } from '../../../hooks/LMS/useEnrolledCoursesData'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Button from '../../../components/UI/Button/Button'
import { LoadingScreen } from '../../../components/UI/LoadingScreen/LoadingScreen'

export default function MyCourse() {
  const { userID: userId } = useContext(UserProfileContext)
  const router = useRouter()

  const { rows, totalCount, loading, loadMoreCourses } = useEnrolledCoursesData(
    0,
    userId,
    8
  )

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="My Course"
            breadcrumbItems={[{ label: 'My Courses' }]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: 'flex',

              width: '22vw',
            }}
          >
            <i
              className={clsx('pi pi-search', style.searchIcon)}
              style={{ top: '22px' }}
            />
            <TextInput theme="metronic" placeholder="Search Course" />
          </span>
        </div>

        {/* <div className={style.tabContainer}>
          <div className="flex gap-4" style={{ height: "2.5rem" }}>
            <Tab
              title={"All"}
              display={true}
              theme="metronic"
              isActive={true}
            />
            <Tab
              title={"Computer Science"}
              display={true}
              theme="metronic"
            />
            <Tab
              title={"Soft Skills"}
              display={true}
              theme="metronic"
            />
          </div>
        </div> */}

        <ConditionalDisplay condition={loading}>
          <LoadingScreen />
        </ConditionalDisplay>

        <ConditionalDisplay condition={!loading}>
          <div className="flex flex-wrap gap-4 justify-content-between">
            {rows?.map((courseMapping, index) => (
              <CourseCard
                key={`course-${index}`}
                courseTitle={courseMapping?.course?.title || 'Course Title'}
                courseDescription={courseMapping?.course?.createdUser || 'User'}
                assignedDate={
                  courseMapping?.course?.overview ||
                  `This is a ${courseMapping?.course?.title} course`
                }
                buttonAction={() =>
                  router.push(`/LMS/MyCourses/${courseMapping?.id}`)
                }
                thumbnailUrl={courseMapping?.course?.thumbnailUrl}
                hasLeadEnrolled={courseMapping?.course?.hasLeadEnrolled}
              />
            ))}

            {/* Placeholder cards to fill the row */}
            {Array.from({ length: (4 - (rows?.length % 4)) % 4 }).map(
              (_, index) => {
                if (rows?.length < 4 && index === 2) {
                  return <PendingCard />
                }
                return (
                  <div
                    key={`placeholder-${index}`}
                    className={clsx(
                      'flex justify-content-center align-items-center',
                      style.card
                    )}
                  >
                    <Image src={emptyCard} alt="Empty Course Placeholder" />
                  </div>
                )
              }
            )}
          </div>

          {/* Load more button */}
          <ConditionalDisplay condition={rows?.length < totalCount}>
            <div className="flex justify-content-center mt-5">
              <Button
                onClick={loadMoreCourses}
                disabled={loading}
                theme="metronic"
                variant="outline"
                label={loading ? 'Loading...' : 'Load More Courses'}
              />
            </div>
          </ConditionalDisplay>
        </ConditionalDisplay>

        {/* <DataTable
          header={tableheader}
          value={mockRows}
          className="custom-lead"
        >
          {mockColumns.map((col, index) => (
            <Column
              key={index}
              field={col.field}
              header={col.header}
              {...(col.body ? { body: col.body } : {})}
            />
          ))}
        </DataTable> */}
      </div>
    </PageContainer>
  )
}
