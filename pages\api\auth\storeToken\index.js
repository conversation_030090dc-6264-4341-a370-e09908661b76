import { serialize } from 'cookie'
import { encryptToken } from '../../../../utillites/encrypt'

export default function handler(req, res) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' })
  }

  const { accessToken } = req.body

  if (!accessToken) {
    return res.status(400).json({ error: 'Access token is required' })
  }

  // Set the token in an HttpOnly cookie

  const encryptAccessToken = encryptToken(accessToken)

  res.setHeader(
    'Set-Cookie',
    serialize('accessToken', encryptAccessToken, {
      httpOnly: true,
      secure: true,
      sameSite: 'Strict',
      path: '/',
      localStorage: true,
    })
  )

  return res.status(200).json({ message: 'Token stored successfully' })
}
