import { getFormDefinition } from '../../../../api/apiCalls'
import { Client } from '../../../PurchaseOrder/[id]'
import View from '../../../view/[id]'

export default function Home({
  id,
  metadata,
  initialConditions,
  initialValues,
  footer,
  formDefinition,
}) {
  return (
    <Client>
      <View
        id={id}
        metadata={metadata}
        initialConditions={initialConditions}
        initialValues={initialValues}
        footer={footer}
        formDefinition={formDefinition}
      />
    </Client>
  )
}

export async function getServerSideProps(context) {
  const { id } = context.params

  try {
    const res = await getFormDefinition(id)

    const initialValues = {}
    if (res.data?.metadata?.metadata?.form) {
      const metadata = res.data.metadata.metadata.form ?? {}
      Object.keys(metadata).forEach((key) => {
        const element = metadata[key]
        if (element.defaultValue) {
          initialValues[element.name] = element.defaultValue
        }
      })
    }

    return {
      props: {
        id,
        metadata: res.data.metadata.metadata.form ?? {},
        initialConditions: res.data.metadata.metadata.conditions ?? {},
        footer: res.data.footer,
        initialValues,
        formDefinition: res.data,
      },
    }
  } catch (err) {
    console.error(err)
    return {
      props: {
        data: [],
      },
    }
  }
}
