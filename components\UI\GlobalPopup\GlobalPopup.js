import { useGlobalPopupContext } from '../../../Context/GlobalPopupContext'
import React, { useState, useEffect, useRef, useContext } from 'react'
import { Device } from '@twilio/voice-sdk'
import { getAccessTokenForScopeSilent } from '../../../src/GetAccessTokenForScopeSilent'
import { formBuilderApiRequest } from '../../../src/msalConfig'
import styles from './GlobalPopup.module.css'
import { Calendar } from 'primereact/calendar'
import clsx from 'clsx'
import Image from 'next/image'
import closeIcon from '../../../svg/metronic/close.svg'
import TextInput from '../Input/TextInput/TextInput'
import SelectInput from '../Input/SelectInput/SelectInput'
import TextareaInput from '../Input/TextareaInput/TextareaInput'
import Button from '../Button/Button'
import phoneEnd from '../../../svg/metronic/phone_end.svg'
import phoneCall from '../../../svg/metronic/phone_call.svg'
import { Toast } from 'primereact/toast'
import { useHandleTimelineEvent } from '../../../hooks/LeadGeneration/useHandleTimelineEvent'
import { useApi } from '../../../hooks/useApi'
import { ToastContext } from '../../../pages/_app'
import { useAccount, useMsal } from '@azure/msal-react'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const GlobalPopup = () => {
  const { isOpen, handleClose, isSendSms, phoneNumber, personalInfo } =
    useGlobalPopupContext()
  const { handleTimelineEvent } = useHandleTimelineEvent()
  const toast = useRef(null)
  const [isClosing, setIsClosing] = useState(false)
  const [token, setToken] = useState(null)
  const [device, setDevice] = useState(null)
  const [call, setCall] = useState(null)
  const [status, setStatus] = useState('Ready to call...')
  const [isCallInProgress, setIsCallInProgress] = useState(false)
  const [deviceReady, setDeviceReady] = useState(false)
  const [callMade, setCallMade] = useState(false)
  const [callStartTime, setCallStartTime] = useState(null)
  const [showLogCompleted, setShowLogCompleted] = useState(false)
  const [callDuration, setCallDuration] = useState({ minutes: 0, seconds: 0 })
  const [isInitializingDevice, setIsInitializingDevice] = useState(false)

  // Add shared call details state
  const [callDetails, setCallDetails] = useState('')

  // Remove the old useEffect that initialized device on mount
  useEffect(() => {
    return () => {
      if (device) {
        device.destroy()
      }
      if (window.localAudioStream) {
        window.localAudioStream.getTracks().forEach((track) => track.stop())
      }
    }
  }, [device])

  // Modified useEffect to initialize device and make call when needed
  useEffect(() => {
    const initializeAndCall = async () => {
      if (
        isOpen &&
        phoneNumber &&
        !call &&
        !isCallInProgress &&
        !callMade &&
        !showLogCompleted &&
        !isSendSms &&
        !isInitializingDevice
      ) {
        setIsInitializingDevice(true)

        // Initialize device first
        const deviceInitialized = await initializeDevice()

        if (deviceInitialized) {
          // Then make the call
          await makeOutgoingCall(deviceInitialized)
          setCallMade(true)
        }

        setIsInitializingDevice(false)
      }
    }

    initializeAndCall()
  }, [
    isOpen,
    phoneNumber,
    call,
    isCallInProgress,
    showLogCompleted,
    isSendSms,
    isInitializingDevice,
  ])

  const initializeDevice = async () => {
    try {
      setStatus('Requesting Access Token...')

      const accessToken = await getAccessTokenForScopeSilent(
        formBuilderApiRequest
      )
      const response = await fetch(`${api}Twilio/Token`, {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      })
      const data = await response.json()

      setStatus('Got a token.')
      setToken(data.token)

      const newDevice = new Device(data.token, {
        logLevel: 1,
        codecPreferences: ['opus', 'pcmu'],
      })

      return new Promise((resolve, reject) => {
        newDevice.on('registered', () => {
          setStatus('Twilio.Device Ready to make calls!')
          setDeviceReady(true)
          setDevice(newDevice)
          resolve(newDevice)
        })

        newDevice.on('error', (error) => {
          console.error('Twilio device error:', error)
          setStatus(`Twilio.Device Error: ${error.message}`)
          reject(error)
        })

        newDevice.register().catch((registerError) => {
          console.error('Registration error:', registerError)
          setStatus(
            `Registration failed: ${registerError?.message || 'Unknown error'}`
          )
          reject(registerError)
        })
      })
    } catch (err) {
      console.error('Device initialization error:', err)
      setStatus(`Error: ${err.message}`)
      return null
    }
  }

  const hangupCall = () => {
    if (call) {
      call.disconnect()
    }
    setStatus('Call ended.')

    // Calculate duration when hanging up - only if not already calculated
    if (
      callStartTime &&
      callDuration.minutes === 0 &&
      callDuration.seconds === 0
    ) {
      const endTime = new Date()
      const totalSeconds = Math.floor((endTime - callStartTime) / 1000)
      const calculatedDuration = {
        minutes: Math.floor(totalSeconds / 60),
        seconds: totalSeconds % 60,
      }
      setCallDuration(calculatedDuration)
    }

    setIsCallInProgress(false)
    setCall(null)
    setShowLogCompleted(true)
  }

  const makeOutgoingCall = async (deviceToUse = device) => {
    if (!deviceToUse) {
      setStatus('Device not initialized')
      return
    }

    if (!phoneNumber) {
      setStatus('No phone number provided')
      return
    }

    try {
      setStatus(`Ringing...`)

      const params = {
        to: phoneNumber,
        callingDeviceIdentity: deviceToUse?._identity ?? '',
      }

      const newCall = await deviceToUse.connect({ params })
      setCall(newCall)

      newCall.on('accept', () => {
        setStatus('Call in progress...')
        setCallStartTime(new Date())
        setIsCallInProgress(true)
      })

      newCall.on('disconnect', () => {
        console.log('Call disconnecting')
        hangupCall()
      })

      newCall.on('cancel', () => {
        console.log('Call canceled')
        hangupCall()
      })
    } catch (error) {
      console.error('Error making call:', error)
      setStatus(`Error making call: ${error.message}`)
    }
  }

  const handlePopupClose = () => {
    setIsClosing(true)
    console.log('Closing popup...')

    if (call) {
      console.log('Hanging up call on popup close')
      call.disconnect()
      setStatus('Call ended.')

      // Calculate duration only if not already calculated and callStartTime exists
      if (
        callStartTime &&
        callDuration.minutes === 0 &&
        callDuration.seconds === 0
      ) {
        const endTime = new Date()
        const totalSeconds = Math.floor((endTime - callStartTime) / 1000)
        const calculatedDuration = {
          minutes: Math.floor(totalSeconds / 60),
          seconds: totalSeconds % 60,
        }
        setCallDuration(calculatedDuration)
      }
    }

    setTimeout(() => {
      // Don't reset these states immediately - let the component use them first
      setShowLogCompleted(false)
      setCallMade(false)
      setIsCallInProgress(false)
      setCall(null)
      setDeviceReady(false)
      setDevice(null)
      setIsInitializingDevice(false)
      // Reset call details when popup closes
      setCallDetails('')
      // Reset duration and start time after a delay to ensure LogCompletedPopupComponent gets the values
      setTimeout(() => {
        setCallDuration({ minutes: 0, seconds: 0 })
        setCallStartTime(null)
      }, 100)
      handleClose()
      setIsClosing(false)
    }, 300)
  }

  if (!isOpen) {
    return (
      <>
        <Toast ref={toast} />
      </>
    )
  }

  return (
    <>
      <Toast ref={toast} />
      <div className={styles.container} style={{ pointerEvents: 'none' }}>
        <div className={styles.overlay} style={{ pointerEvents: 'none' }}>
          <div
            className={`${styles.content} ${styles.bottomRight} ${
              isClosing ? styles.closing : ''
            }`}
            onClick={(e) => e.stopPropagation()}
            style={{ pointerEvents: 'auto' }}
          >
            {isSendSms ? (
              <SendSmsComponent
                personalInfo={personalInfo}
                onSmsCancel={handlePopupClose}
                phoneNumber={phoneNumber}
                toastRef={toast}
                handleTimelineEvent={handleTimelineEvent}
              />
            ) : !showLogCompleted ? (
              <CallInProgressGlobalPopUpComponent
                personalInfo={personalInfo}
                phoneNumber={phoneNumber}
                hangupCall={hangupCall}
                callStartTime={callStartTime}
                isCallInProgress={isCallInProgress}
                onClose={handlePopupClose}
                callDetails={callDetails}
                setCallDetails={setCallDetails}
              />
            ) : (
              <LogCompletedPopupComponent
                personalInfo={personalInfo}
                onClose={handlePopupClose}
                initialDuration={callDuration}
                callStartTime={callStartTime}
                phoneNumber={phoneNumber}
                handleTimelineEvent={handleTimelineEvent}
                callDetails={callDetails}
                setCallDetails={setCallDetails}
              />
            )}
          </div>
        </div>
      </div>
    </>
  )
}

const CallInProgressGlobalPopUpComponent = ({
  hangupCall,
  callStartTime,
  isCallInProgress,
  onClose,
  phoneNumber,
  personalInfo,
  callDetails,
  setCallDetails,
}) => {
  return (
    <div className={styles.callInProgressContainer}>
      <CallHeader
        callStartTime={callStartTime}
        isCallInProgress={isCallInProgress}
        onClose={onClose}
      />
      <div className="flex flex-column gap-4" style={{ padding: '30px' }}>
        <div className="flex gap-4">
          <TextInput
            disabled
            label="First Name"
            value={personalInfo.firstName}
            theme="metronic"
          />
          <TextInput
            disabled
            label="Last Name"
            value={personalInfo.lastName}
            theme="metronic"
          />
        </div>
        <div className="flex gap-4">
          <TextInput
            disabled
            label="Phone"
            value={phoneNumber}
            theme="metronic"
          />
          <TextInput
            label="Email"
            value={personalInfo.primaryEmail}
            disabled
            theme="metronic"
          />
        </div>
        <div className="flex gap-4">
          <TextInput
            label="Program"
            disabled
            value="Electrical Technician"
            theme="metronic"
          />
          <TextInput
            disabled
            label="Location"
            value="Nevada"
            theme="metronic"
          />
        </div>

        {/* Add the Details textarea to the call in progress component */}

        <TextareaInput
          label="Details"
          value={callDetails}
          onChange={(e) => setCallDetails(e.target.value)}
          rows={4}
          theme="metronic"
        />
      </div>

      <div className={styles.callActions}>
        <Button
          label="End Call"
          className={styles.rejectedButton}
          style={{
            backgroundColor: '#FC3C3C',
            fontWeight: 'bold',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
          }}
          onClick={hangupCall}
          icon={<Image src={phoneEnd} alt="end" />}
        />
      </div>
    </div>
  )
}

const CallHeader = ({ callStartTime, isCallInProgress, onClose }) => {
  const [, forceUpdate] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      forceUpdate((prev) => prev + 1)
    }, 1000)

    return () => clearInterval(timer)
  }, [])

  const formatTime = () => {
    if (!isCallInProgress || !callStartTime) return 'Ringing...' // Show "Ringing..." instead of "00:00"
    const now = new Date()
    const seconds = Math.floor((now - callStartTime) / 1000)

    // Add bounds checking to prevent extremely large values
    if (seconds < 0 || seconds > 86400) {
      // More than 24 hours seems unreasonable
      return '00:00'
    }

    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds
      .toString()
      .padStart(2, '0')}`
  }

  return (
    <div className={styles.callContainer}>
      <div className={styles.callContent}>
        <div className={styles.callTitle}>
          <h1 className={styles.callHeader}>
            {isCallInProgress ? 'Call In Progress' : 'Calling...'}{' '}
            {/* Change header text based on call status */}
          </h1>
          <Image src={phoneCall} alt="call" />
          <div className={styles.callTimer}>{formatTime()}</div>
        </div>
        <div style={{ display: 'flex' }}>
          <div className={styles.headerButtonContainer}>
            <Image
              src={closeIcon}
              alt="close"
              onClick={onClose}
              className="cursor-pointer"
            />
          </div>
        </div>
      </div>
      {/* <div className={styles.progressBar}></div> */}
    </div>
  )
}

const Label = ({ text }) => {
  return <label className={styles.label}>{text}</label>
}

// Update LogCompletedPopupComponent to use the shared details state
const LogCompletedPopupComponent = ({
  personalInfo,
  onClose,
  initialDuration,
  callStartTime,
  phoneNumber,
  handleTimelineEvent,
  handleClose,
  callDetails,
  setCallDetails,
}) => {
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})

  const [subject, setSubject] = useState('Initial Phone Contact')
  const [callType, setCallType] = useState('Outbound')
  // Remove local details state since we're using the shared one
  const [from, setFrom] = useState('Steve Hayden')
  const [to, setTo] = useState('John Smith - $Lead_phone')

  // Better duration initialization
  const [duration, setDuration] = useState(() => {
    // If we have valid initialDuration, use it
    if (
      initialDuration &&
      typeof initialDuration.minutes === 'number' &&
      typeof initialDuration.seconds === 'number' &&
      (initialDuration.minutes > 0 || initialDuration.seconds > 0)
    ) {
      return initialDuration
    }

    // Otherwise, calculate from callStartTime if available
    if (callStartTime) {
      const now = new Date()
      const totalSeconds = Math.floor((now - callStartTime) / 1000)
      return {
        minutes: Math.floor(totalSeconds / 60),
        seconds: totalSeconds % 60,
      }
    }

    // Fallback to zero
    return { minutes: 0, seconds: 0 }
  })

  const [startTime, setStartTime] = useState(callStartTime || new Date())

  // Update duration when initialDuration changes and is valid
  useEffect(() => {
    if (
      initialDuration &&
      typeof initialDuration.minutes === 'number' &&
      typeof initialDuration.seconds === 'number' &&
      (initialDuration.minutes > 0 || initialDuration.seconds > 0)
    ) {
      setDuration(initialDuration)
    }
  }, [initialDuration])

  // Update startTime when callStartTime changes
  useEffect(() => {
    if (callStartTime) {
      setStartTime(callStartTime)
    }
  }, [callStartTime])

  const toast = useContext(ToastContext)
  const { callApi, loading } = useApi()

  const formatCallData = (data) => {
    const name = data.to.split(' - ')[0]
    // Format the start time with AM/PM
    const formattedStartTime = new Date(data.startTime).toLocaleString(
      'en-US',
      {
        month: 'numeric',
        day: 'numeric',
        year: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true,
      }
    )

    return `<b>Subject:</b> ${data.subject}<br/>
<b>Call Type:</b> ${data.callType}<br/>
<b>From:</b> ${data.from}<br/>
<b>To:</b> ${name} - ${phoneNumber}<br/>
<b>Start Time:</b> ${formattedStartTime}<br/>
<b>Duration:</b> ${data.totalCallSeconds} seconds<br/>
<b>Details:</b> ${data.details || 'No details provided'}`
  }

  const handleSave = async () => {
    const callData = {
      leadId: personalInfo?.leadId,
      subject,
      callType,
      details: callDetails, // Use shared callDetails state
      from,
      to,
      startTime: startTime,
      totalCallSeconds: duration.minutes * 60 + duration.seconds,
    }

    try {
      const res = await fetch(`${api}CallLog`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(callData),
      })

      const data = await res.json()
      if (data) {
        const result = await callApi({
          method: 'PATCH',
          url: 'LeadsWorkflow',
          data: {
            leadId: personalInfo?.leadId,
            stage: 4,
          },
        })
        if (result) {
          toast?.current?.show({
            severity: 'success',
            summary: 'Successful',
            detail: 'Engagement call Log Saved',
            life: 3000,
            className: 'custom-lead',
          })
        }
      }
      if (data) {
        handleTimelineEvent(
          'Phone call logged',
          personalInfo?.leadId,
          personalInfo?.stagesJson,
          personalInfo?.displayName,
          formatCallData(callData),
          personalInfo?.fetchLeadDetails
        )
      }
    } catch (error) {
      console.error('Error saving call log:', error)
    } finally {
      onClose()
    }
  }

  return (
    <div className={styles.logCompletedContainer}>
      <div className={styles.logHeader}>
        <h1 className={clsx('py-4', styles.logHeaderTitle)}>
          Log Completed Call
        </h1>
        <Image src={closeIcon} alt="close" onClick={onClose} />
      </div>
      <div style={{ padding: '30px' }}>
        <div className="flex gap-4">
          <TextInput
            label="Subject"
            type="text"
            value={subject}
            onChange={(e) => setSubject(e.target.value)}
            theme="metronic"
          />
          <SelectInput
            label="Call Type"
            value={callType}
            onChange={(e) => setCallType(e.value)}
            options={[
              { label: 'Inbound', value: 'Inbound' },
              { label: 'Outbound', value: 'Outbound' },
            ]}
            theme="metronic"
          />
        </div>
        <div className="mt-4">
          <TextareaInput
            label="Details"
            value={callDetails} // Use shared callDetails state
            onChange={(e) => setCallDetails(e.target.value)} // Use shared setter
            rows={4}
            theme="metronic"
          />
        </div>
        <div className="flex gap-4 mt-4">
          <TextInput
            label="From"
            value={account?.name}
            disabled
            theme="metronic"
          />
          <TextInput
            label="To"
            value={`${personalInfo?.firstName} ${personalInfo?.lastName} - ${phoneNumber}`}
            disabled
            theme="metronic"
          />
        </div>
        <div className="flex gap-4 mt-4">
          <div className={styles.formField} style={{ width: '100%' }}>
            <Label text="Start Time" />
            <Calendar
              value={startTime}
              onChange={(e) => setStartTime(e.value)}
              timeOnly
              hourFormat="12"
              className="custom-lead"
            />
          </div>
          <TextInput
            label="Call Duration"
            value={`${duration.minutes} mins ${duration.seconds} secs`}
            disabled
            theme="metronic"
          />
        </div>
        <div className="text-right mt-4">
          <Button
            label="Save"
            onClick={handleSave}
            theme="metronic"
            width="150px"
            loading={loading}
          />
        </div>
      </div>
    </div>
  )
}

const SendSmsComponent = ({
  phoneNumber,
  personalInfo,
  onSmsCancel,
  toastRef,
  onSmsSend,
  handleTimelineEvent,
}) => {
  const [message, setMessage] = useState('')
  const [isSending, setIsSending] = useState(false)
  const [sentStatus, setSentStatus] = useState(null)
  const [fromNumber, setFromNumber] = useState('+17025550123')

  const fromOptions = [
    { label: '$agentsPhoneNumber', value: '+17025550123' },
    { label: '$companyPhoneNumber', value: '+18881234567' },
  ]

  const formatSmsData = () => {
    return `<b>From:</b> ${fromNumber}<br/>
<b>To:</b> ${phoneNumber}<br/>
<b>Message:</b> ${message}`
  }

  const handleSendSms = async () => {
    if (!message.trim() || !fromNumber) return

    try {
      setIsSending(true)
      const accessToken = await getAccessTokenForScopeSilent(
        formBuilderApiRequest
      )
      const response = await fetch(`${api}Twilio/SendSms`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${accessToken}`,
        },
        body: JSON.stringify({
          leadId: personalInfo.leadId,
          to: phoneNumber,
          body: message,
        }),
      })

      if (!response.ok) {
        throw new Error('Failed to send SMS')
      }

      const data = await response.json()

      if (toastRef && toastRef.current) {
        toastRef.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: 'SMS Sent',
          life: 3000,
        })
      }

      if (data) {
        handleTimelineEvent(
          'SMS sent',
          personalInfo?.leadId,
          personalInfo?.stagesJson,
          personalInfo?.displayName,
          formatSmsData(),
          personalInfo?.fetchLeadDetails
        )
      }

      onSmsCancel()
    } catch (error) {
      if (toastRef && toastRef.current) {
        toastRef.current.show({
          severity: 'success',
          summary: 'Successful',
          detail: 'SMS Sent',
          life: 3000,
        })
      }
      onSmsCancel()
      console.error('Error sending SMS:', error)
    } finally {
      setIsSending(false)
    }
  }

  return (
    <div className={styles.smsFormContainer}>
      <div className={styles.smsHeader}>
        <div className={styles.logHeader}>
          <h1 className={clsx('py-4', styles.logHeaderTitle)}>SMS</h1>
          <Image src={closeIcon} alt="close" onClick={onSmsCancel} />
        </div>
      </div>

      <div style={{ padding: '30px' }}>
        {/* First row: From and To fields */}
        <div className="flex gap-4 mb-4">
          <SelectInput
            label="From"
            options={fromOptions}
            value={fromNumber}
            onChange={(e) => setFromNumber(e.value)}
            theme="metronic"
            placeholder="Select a number"
          />
          <TextInput label="To" value={phoneNumber} disabled theme="metronic" />
        </div>

        {/* Second row: Message text area */}
        <div className="mb-4">
          <TextareaInput
            label="Message"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            rows={6}
            theme="metronic"
            placeholder="Type your message here..."
            required
          />
          <div className="text-right text-xs text-gray-500 mt-1">
            {message.length}/160 characters
          </div>
        </div>
      </div>
      <div className={styles.callActions}>
        <Button
          label="Cancel"
          className={styles.rejectedButton}
          onClick={onSmsCancel}
          style={{
            color: '#515151',
            backgroundColor: '#e7f1fd',
            fontWeight: 'bold',
            border: 'none',
            cursor: 'pointer',
          }}
        />
        <Button
          label="Send"
          className={styles.rejectedButton}
          onClick={handleSendSms}
          style={{
            backgroundColor: '#1b84ff',
            fontWeight: 'bold',
            border: 'none',
            color: '#fff',
            cursor: 'pointer',
          }}
          disabled={isSending}
        />
      </div>
    </div>
  )
}
