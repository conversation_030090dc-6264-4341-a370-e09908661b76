import PrimaryButton from '../UI/PrimaryButton/PrimaryButton'
import RoleBasedPage from '../../public/UserProfileContext/RoleBasedPage'
import ViewComponents from '../FormBuilder/ViewComponents/ViewComponents/ViewComponents'
import Navbar from '../UI/Navbar/Navbar'
import SideNavbar from '../UI/SideNavbar/SideNavbar'
import PageCountDisplay from '../UI/PageCountDisplay/PageCountDisplay'
import BreadCrumbs from '../UI/BreadCrumbs/BreadCrumbs'
import { Card } from 'primereact/card'
import { Toast } from 'primereact/toast'
import { AuthenticatedTemplate } from '@azure/msal-react'
import { useState, useRef, useMemo } from 'react'
import { useRouter } from 'next/router'
import { useValidation } from '../../hooks/useValidation'
import { useUpdateMetadata } from '../../hooks/useUpdateMetadata'
import { useFormSubmission } from '../../hooks/useFormSubmission'
import { useSubmitFormValidation } from '../../hooks/useSubmitFormValidation'
import { PageContainer } from '../UI/Page/PageContainer/PageContainer'
import { ConditionalDisplay } from '../UI/ConditionalDisplay/ConditionalDisplay'
import { Container } from '../UI/Layouts/Layouts'
import { SaveChangesContainer } from '../UI/SaveChangesContainer/SaveChangesContainer'
import { PageCountDisplayContainer } from '../UI/PageCountDisplayContainer/PageCountDisplayContainer'
import { LoadingScreen } from '../UI/LoadingScreen/LoadingScreen'
import { useGetFormSubmisisonInputs } from '../../hooks/FormSubmission/useGetFormSubmissionInputs'
import SaveIcon from '../../svg/White save_line.svg'
import styles from './FormSubmission.module.css'
import { Button } from 'primereact/button'
import Image from 'next/image'
import clsx from 'clsx'
import PageProgress from '../UI/PageProgress/PageProgress'
import { useCheckSameFormSubInputs } from '../../hooks/FormSubmission/useCheckSameFormSubInputs'
import { useCheckSpreadsheetMandatoryCells } from '../../hooks/FormSubmission/useCheckSpreadsheetMandatoryCells'
import { useCheckMandatoryGridCells } from '../../hooks/FormSubmission/useCheckMandatoryGridCells'

export default function FormSubmission({
  FormTabs,
  breadCrumbObj,
  showDiscardButton = true,
  isConnectPage = false,
}) {
  const router = useRouter()
  const { id } = router.query

  const {
    formDefinition,
    loadingFormDefinition,
    metadata,
    setMetadata,
    conditions,
    inputs,
    setInputs,
    files,
    setFiles,
    handleInputChange,
    assignValuesNested,
  } = useGetFormSubmisisonInputs(id)

  const toast = useRef(null)
  const solutionId = formDefinition?.formDefinitionMaster?.solution?.id
  const isDraft = true
  const isNotDraft = false

  const [currentPage, setCurrentPage] = useState(0)
  const totalPagesArray =
    metadata && Object.keys(metadata).length > 0
      ? [
          ...new Set(
            Object.keys(metadata).map((key) => metadata[key].pageNumber)
          ),
        ]
      : [0]

  const { errors, validationMapper } = useValidation({
    metadata,
    inputs,
    files,
  })
  const { getUpdatedMetadata, updatedMetadataWithConditions } =
    useUpdateMetadata({
      conditions,
      metadata,
      validationMapper,
      inputs,
      currentPage,
    })

  const { pageData, pageBreaks } = getUpdatedMetadata()

  const { checkErrors } = useSubmitFormValidation({
    metadata: updatedMetadataWithConditions,
    inputs,
    files,
    setInputs,
    setFiles,
  })

  const { checkInputs } = useCheckSameFormSubInputs()

  const { checkMandatorySpreadsheetColumns } =
    useCheckSpreadsheetMandatoryCells({
      metadata,
      inputs,
    })

  const { checkMandatoryGridCells } = useCheckMandatoryGridCells({
    metadata,
    inputs,
  })

  const {
    postFormSubmission,
    postingFormSubmission,
    savingDraft,
    setPostingFormSubmission,
  } = useFormSubmission({
    formDefinition,
    errors,
    toastRef: toast,
    metadata,
    inputs,
    files,
    checkErrors,
    checkInputs,
    checkMandatorySpreadsheetColumns,
    checkMandatoryGridCells,
  })

  const submissionData = useMemo(
    () => ({
      isDraft,
      metadata,
      inputs,
      files,
    }),
    [inputs, files]
  )

  if (loadingFormDefinition) {
    return <LoadingScreen />
  }

  return (
    <AuthenticatedTemplate>
      <Navbar router={router} solutionId={solutionId} />
      <Container>
        <SideNavbar solutionId={solutionId} />
        <RoleBasedPage
          authorizedRoles={[
            'Creator',
            'Viewer',
            'Contributor',
            'Designer',
            'End User',
          ]}
          solutionId={solutionId}
        >
          <PageContainer width={'100%'}>
            <Toast ref={toast} />

            {/* TODO: Refactor the functions in useFormSubmission e.g. postFormSubmission so that the below code will function. */}
            {/* <div style={{ display: 'flex', alignItems: 'center' }}>
              <BreadCrumbs
                title={breadCrumbObj?.title}
                breadcrumbItems={breadCrumbObj?.breadcrumbItems}
              />

              <SubmissionControls 
                saveFunction={postFormSubmission} 
                saveObj={submissionData} 
                showDiscardButton={showDiscardButton}
                isConnectPage={isConnectPage}
                status={formDefinition?.status}
              />
            </div> */}

            <div style={{ display: 'flex', alignItems: 'center' }}>
              <BreadCrumbs
                title={breadCrumbObj?.title}
                breadcrumbItems={breadCrumbObj?.breadcrumbItems}
              />

              {formDefinition?.status === 1 || isConnectPage ? (
                <div style={{ gap: '20px', display: 'flex' }}>
                  <div>
                    <Button
                      onClick={(e) => postFormSubmission(e, isDraft)}
                      disabled={savingDraft}
                      className={styles.saveButtonView}
                      label="Save Draft"
                      loading={savingDraft}
                      icon={
                        <Image
                          src={SaveIcon}
                          alt="Save"
                          width={20}
                          height={20}
                          className={styles.saveIcon}
                        />
                      }
                    />
                  </div>
                  {formDefinition?.hasObjectLinking && (
                    <div>
                      <Button
                        onClick={(e) => {
                          postFormSubmission(e, isNotDraft, true)

                          const newInputs = {}
                          Object.keys(inputs).forEach((key) => {
                            if (key.startsWith('text')) {
                              newInputs[key] = ''
                            }
                          })

                          setInputs(newInputs)
                        }}
                        className={styles.saveButtonView}
                        label="Save Form"
                        loading={postingFormSubmission}
                        icon="pi pi-check"
                      />
                    </div>
                  )}
                  {showDiscardButton && (
                    <div>
                      <Button
                        onClick={confirmDiscard}
                        className={styles.saveButtonView}
                        label="Discard Draft"
                        icon={clsx('pi pi-trash', styles.saveIcon)}
                      />
                    </div>
                  )}
                </div>
              ) : null}
            </div>

            <FormTabs
              formTitle={formDefinition?.name}
              solutionId={solutionId}
            />
            <PageProgress
              totalPages={totalPagesArray.length}
              currentPage={currentPage}
              pageBreaks={pageBreaks}
              setCurrentPage={setCurrentPage}
              pageDisplayOption={formDefinition?.pageDisplayOption}
            />
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <Card
                className="card form-horizontal"
                style={{
                  width: '100%',
                  marginRight: '0px',
                  marginLeft: '0px',
                  overflowX: 'hidden',
                  marginBottom: '0px',
                }}
              >
                <ConditionalDisplay condition={formDefinition?.status == 1}>
                  <ViewComponents // Don't change this for the onboarding accelerator
                    metadata={metadata}
                    setMetadata={setMetadata}
                    validationMapper={validationMapper}
                    inputs={inputs}
                    setInputs={setInputs}
                    files={files}
                    handleInputChange={handleInputChange}
                    assignValuesNested={assignValuesNested}
                    errors={errors}
                    checkErrors={checkErrors}
                    currentPage={currentPage}
                    setCurrentPage={setCurrentPage}
                    isSubmissionPage={true}
                    pageData={pageData}
                    objectKeysArray={totalPagesArray}
                    metadataWithPermissions={updatedMetadataWithConditions}
                    status={formDefinition?.status}
                  />
                  <SaveChangesContainer>
                    {!formDefinition?.hasObjectLinking && (
                      <PrimaryButton
                        buttonstyle={{
                          marginRight: '5rem',
                          marginBottom: '20px',
                        }}
                        disabled={currentPage !== totalPagesArray.length - 1}
                        className={styles.submitButton}
                        text="Submit"
                        icon="pi pi-check"
                        onClick={(e) => {
                          setPostingFormSubmission(true)
                          postFormSubmission(e, isNotDraft, false)
                        }}
                        loading={postingFormSubmission}
                      />
                    )}
                  </SaveChangesContainer>
                  <ConditionalDisplay
                    condition={!formDefinition?.pageDisplayOption === 1}
                  >
                    <PageCountDisplayContainer>
                      <PageCountDisplay
                        pageNumber={currentPage}
                        totalPagesLength={totalPagesArray.length}
                        footer={formDefinition.footer}
                      />
                    </PageCountDisplayContainer>
                  </ConditionalDisplay>
                </ConditionalDisplay>
                <ConditionalDisplay condition={formDefinition?.status != 1}>
                  <FormDeactivated />
                </ConditionalDisplay>
              </Card>
            </div>
          </PageContainer>
        </RoleBasedPage>
      </Container>
    </AuthenticatedTemplate>
  )
}

const FormDeactivated = () => {
  return (
    <h1 className={styles.formDeactivatedHeader}>
      This form is deactivated. For more information, contact your administrator
    </h1>
  )
}
