import Image from "next/image";
import { PageContainer } from "../../../../components/UI/Page/PageContainer/PageContainer";
import Backarrow from "../../../../svg/metronic/back_metronic.svg";
import BreadCrumbs from "../../../../components/UI/BreadCrumbs/BreadCrumbs";
import Button from "../../../../components/UI/Button/Button";
export default function NewAssignment() {
  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Create New Assignment"
            breadcrumbItems={[{ label: "Create New Assignment" }]}
            theme="metronic"
          />
          <Button label="Next" theme="metronic" width="150px" />
        </div>
      </div>
    </PageContainer>
  );
}