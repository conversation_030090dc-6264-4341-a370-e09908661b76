// Library components/hooks
import { Card } from 'primereact/card'
import {
  AuthenticatedTemplate,
  UnauthenticatedTemplate,
} from '@azure/msal-react'
import { useState } from 'react'
import { Toast } from 'primereact/toast'
import { useRouter } from 'next/router'

// Custom components/hooks
import ApprovalForm from '../../../../../components/WorkflowBuilder/WorkflowNode/Form/ApprovalForm'
import Modal from '../../../../../components/UI/Modal/Modal'
import BreadCrumbs from '../../../../../components/UI/BreadCrumbs/BreadCrumbs'
import RoleBasedPage from '../../../../../public/UserProfileContext/RoleBasedPage'
import SubmissionViewPage from '../../../../../components/UI/SubmissionViewPage/SubmissionViewPage'
import SideNavbar from '../../../../../components/UI/SideNavbar/SideNavbar'
import Navbar from '../../../../../components/UI/Navbar/Navbar'
import FormApproval from '../../../../../components/FormApproval/FormApproval'
import { ApprovalOptions } from '../../../../../components/UI/ApprovalOptions/ApprovalOptions'
import {
  getFormDefinition,
  getFormSubmission,
} from '../../../../../api/apiCalls'
import { ApproverStatus } from '../../../../../components/WorkflowBuilder/WorkflowNode/ApproverStatus/ApproverStatus'
import { ConditionalDisplay } from '../../../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { PageContainer } from '../../../../../components/UI/Page/PageContainer/PageContainer'
import { PageElementContainer } from '../../../../../components/UI/Page/PageElementContainer/PageElementContainer'
import { AuditHistoryDashboard } from '../../../../../components/UI/Dashboards/AuditHistoryDashboard/AuditHistoryDashboard'
import { RelationshipsDashboard } from '../../../../../components/UI/Dashboards/RelationshipsDashboard/RelationshipsDashboard'
import { VotingResultsDashboard } from '../../../../../components/UI/Dashboards/VotingResultsDashboard/VotingResultsDashboard'
import { Container as LayoutContainer } from '../../../../../components/UI/Layouts/Layouts'
import { AccessRequestSummaryList } from '../../../../../components/UI/AccessRequestSummaryList/AccessRequestSummaryList'
import { useConcatenate } from '../../../../../hooks/useConcatenate'
import { useCondition } from '../../../../../hooks/FormBuilder/conditionHooks'
import { usePreventSubmit } from '../../../../../hooks/usePreventSubmit'
import { useInputs } from '../../../../../hooks/useInput'
import { useValidation } from '../../../../../hooks/useValidation'
import { useUpdateMetadata } from '../../../../../hooks/useUpdateMetadata'
import { useSubmitFormValidation } from '../../../../../hooks/useSubmitFormValidation'

// Form Approval Hooks
import { useFetchFormApprovalData } from '../../../../../hooks/FormApproval/useFetchFormApprovalData'
import { useFormApprovalActions } from '../../../../../hooks/FormApproval/useFormApprovalActions'
import { useGetFormApprovalRevision } from '../../../../../hooks/FormApproval/useGetFormApprovalRevision'
import { useSaveFormApprovalChanges } from '../../../../../hooks/FormApproval/useSaveFormApprovalChanges'
import VotingDashboard from '../../../../../components/UI/Dashboards/VotingDashboard/VotingDashboard'
import { useGetFormDefinition } from '../../../../../hooks/useGetFormDefinition'
import { getAccessToken } from '../../../../../utillites/getAccessToken'
import UnauthorizedPage from '../../../../401'
import { encryptToken } from '../../../../../utillites/encrypt'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export default function FormSubmissionView({
  id,
  fid,
  dashboard,
  initialFormSubmissionData,
  breadCrumbObj,
  confirmDiscard,
  handleDiscard,
  closeDialog,
  token,
  error,
}) {
  console.log(
    'FormSubmissionView',
    id,
    fid,
    dashboard,
    initialFormSubmissionData
  )
  const { formDefinition, loadingFormDefinition } = useGetFormDefinition({
    formDefinitionId: id,
    formSubmissionId: fid,
  })
  const metadata = formDefinition?.metadata?.metadata.form ?? {}
  const [formSubmissionData, setFormSubmissionData] = useState(
    initialFormSubmissionData
  )
  const [isVisible, setIsVisible] = useState(false)
  const [currentPage, setCurrentPage] = useState(0)
  const [type, setType] = useState('APPROVING')

  const router = useRouter()
  const {
    inputs,
    setInputs,
    handleInputChange,
    files,
    assignValuesNested,
    setFiles,
  } = useInputs({ initialValues: formSubmissionData })

  const { formSubmission, formSubmissionMaster, refetchFormSubmissionMaster } =
    useFetchFormApprovalData({ fid, formSubmissionGuid: router.query.fid })

  const uniqueAuthorLegalNames = [
    ...new Set(
      formSubmission?.formTransactions?.map(
        (transaction) => transaction.authorLegalName
      )
    ),
  ]

  const { checkSamePersonForSignature } = usePreventSubmit({
    metadata,
    inputs,
    setInputs,
    files,
    setFiles,
    uniqueAuthorLegalNames,
  })
  const { getInputsForConcat } = useConcatenate({ metadata, inputs })
  const solutionId = formDefinition?.formDefinitionMaster?.solution?.id
  const workflowEmails = formSubmission?.formTimelines?.map(
    (item) => item?.user?.email || item?.userGroup?.groupName
  )
  const totalPagesArray =
    Object.keys(metadata ?? {}).length > 0
      ? [
          ...new Set(
            Object.keys(metadata).map((key) => metadata[key].pageNumber)
          ),
        ]
      : [0]

  const { saveChangesDialog, setSaveChangesDialog, saveChangesCondition } =
    useSaveFormApprovalChanges({
      finalizedForm: formSubmission?.isFinalized,
    })

  const { errors, validationMapper } = useValidation({
    metadata,
    inputs,
    authorName: formSubmission?.userFullLegalName,
  })

  const { conditionMapper } = useCondition({
    initialConditions: formDefinition?.metadata?.metadata?.conditions,
  })

  const {
    selectedRevision,
    setSelectedRevision,
    revisionHistoryOptions,
    handleRevisionChange,
    isActiveRevision,
  } = useGetFormApprovalRevision({
    formSubmissionMaster,
    formSubmission,
    setFormSubmissionData,
    setInputs,
  })
  const { getUpdatedMetadata, updatedMetadataWithConditions } =
    useUpdateMetadata({
      conditions: formDefinition?.metadata?.metadata?.conditions,
      metadata,
      validationMapper,
      inputs,
      currentPage,
    })

  const { pageData, objectKeysArray, pageBreaks } = getUpdatedMetadata()
  const { checkErrors } = useSubmitFormValidation({
    metadata: updatedMetadataWithConditions,
    inputs,
    setInputs,
    files,
    setFiles,
  })

  const {
    updateFormSubmission,
    deleteFormSubmission,
    submitLoading,
    formApprovalToast,
    loading,
    account,
  } = useFormApprovalActions({
    fid,
    formSubmission,
    inputs,
    files,
    checkErrors,
    checkSamePersonForSignature,
    refetchFormSubmissionMaster,
    setSaveChangesDialog,
    setSelectedRevision,
    getInputsForConcat,
    solutionId,
    metadata,
    formDefinition,
  })

  const onHide = () => {
    setIsVisible((prev) => !prev)
  }

  const showSuccess = () => {
    formApprovalToast.current.show({
      severity: `${type === 'APPROVING' ? 'success' : 'warn'}`,
      summary: `${type === 'APPROVING' ? 'Approved' : 'Rejected'}`,
      life: 3000,
    })
  }

  const breadcrumbItems = [
    {
      label:
        dashboard === 'approvals'
          ? 'Approvals'
          : dashboard === 'mySubmissions' && solutionId !== 2
            ? 'My Submissions'
            : dashboard === 'myRequest'
              ? 'My Request'
              : solutionId === 2
                ? 'Performance Review'
                : dashboard,
    },
    { label: 'Forms' },
    { label: 'Waiting For Approval' },
  ]

  const formTitleStyle = {
    fontWeight: 'bold',
    fontSize: '1.10rem',
    color: 'var(--primary-bg-blackPearl)',
    backgroundColor: 'var(--primary-bg-darkCerulean-10)',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '40px',
    borderRadius: '5px',
    textTransform: 'capitalize',
  }

  if (error) {
    return <UnauthorizedPage token={token} error={error} />
  }

  return (
    <>
      <AuthenticatedTemplate key={fid}>
        <Navbar router={router} solutionId={solutionId} />
        <LayoutContainer>
          <SideNavbar solutionId={solutionId} />
          <ConditionalDisplay
            condition={formSubmission?.statusName === 'Draft'}
          >
            <SubmissionViewPage
              account={account}
              toast={formApprovalToast}
              updateFormSubmission={updateFormSubmission}
              loading={submitLoading}
              formDefinition={formDefinition}
              metadata={metadata}
              conditionMapper={conditionMapper}
              validationMapper={validationMapper}
              inputs={inputs}
              setInputs={setInputs}
              files={files}
              handleInputChange={handleInputChange}
              assignValuesNested={assignValuesNested}
              errors={errors}
              formSubmission={formSubmission}
              formSubmissionData={formSubmissionData}
              footer={formDefinition?.footer}
              getInputsForConcat={getInputsForConcat}
              currentPage={currentPage}
              setCurrentPage={setCurrentPage}
              totalPagesArray={totalPagesArray}
              deleteFormSubmission={deleteFormSubmission}
              pageData={pageData}
              pageBreaks={pageBreaks}
              metadataWithPermissions={updatedMetadataWithConditions}
              objectKeysArray={objectKeysArray}
              fid={fid}
              breadCrumbObj={breadCrumbObj}
              confirmDiscard={confirmDiscard}
              handleDiscard={handleDiscard}
              closeDialog={closeDialog}
              solutionId={solutionId}
            />
          </ConditionalDisplay>
          <ConditionalDisplay
            condition={formSubmission?.statusName !== 'Draft'}
          >
            <RoleBasedPage
              authorizedRoles={[
                'Contributor',
                'Viewer',
                'Creator',
                'Designer',
                'End User',
              ]}
              originalSubmitter={formSubmission?.userEmail}
              workflowEmails={workflowEmails}
              solutionId={solutionId}
            >
              <PageContainer>
                <BreadCrumbs
                  title={formSubmission?.formDefinition?.name}
                  breadcrumbItems={breadcrumbItems}
                  className="mb-3"
                />
                <ApprovalOptions
                  checkErrors={checkErrors}
                  style={{ display: 'flex' }}
                  solutionId={solutionId}
                  formSubmission={formSubmission}
                  inputs={inputs}
                  isActiveRevision={isActiveRevision}
                  metadata={metadata}
                  fid={fid}
                />
                <ApproverStatus
                  formId={id}
                  formSubmissionId={router.query.fid}
                  formSubmission={formSubmission}
                />
                <FormApproval
                  fid={fid}
                  metadata={metadata}
                  formDefinitionData={formDefinition}
                  initialFormSubmissionData={initialFormSubmissionData}
                  conditions={formDefinition?.metadata?.metadata?.conditions}
                />
                {/* <ConditionalDisplay condition={solutionId === 1 || solutionId === 4 || solutionId === 5 || solutionId === 6}> */}
                <ConditionalDisplay
                  condition={formSubmission?.formTimelines?.some(
                    (timeline) => timeline.isVotingRequired
                  )}
                >
                  <PageElementContainer marginTop="0px">
                    <VotingDashboard
                      formSubmission={formSubmission}
                    ></VotingDashboard>
                  </PageElementContainer>
                </ConditionalDisplay>
                <PageElementContainer marginTop="0px">
                  <RelationshipsDashboard formSubmission={formSubmission} />
                </PageElementContainer>
                <PageElementContainer>
                  <AuditHistoryDashboard
                    formTransactions={formSubmission?.formTransactions}
                    formMetadata={metadata}
                    loading={loading}
                  />
                </PageElementContainer>
                <Modal
                  header={'Approver'}
                  visible={isVisible}
                  onHide={onHide}
                  style={{ width: '50wv' }}
                >
                  <ApprovalForm
                    type={type}
                    formId={id}
                    metaDataId={router.query.fid}
                    submissionId={id}
                    onHide={onHide}
                    showSuccess={showSuccess}
                  />
                </Modal>
                <Toast ref={formApprovalToast} />
                {formDefinition?.formDefinitionMaster?.solution?.id === 7 &&
                  formDefinition?.name === 'Access Request' && (
                    <Card
                      className="card form-horizontal mt-5"
                      style={{
                        width: '100%',
                        marginRight: '20px',
                        marginLeft: '20px',
                      }}
                    >
                      <div style={formTitleStyle}>Access Request Summary</div>
                      <AccessRequestSummaryList
                        pageData={pageData}
                        metadata={metadata}
                        inputs={inputs}
                      />
                    </Card>
                  )}
              </PageContainer>
            </RoleBasedPage>
          </ConditionalDisplay>
        </LayoutContainer>
      </AuthenticatedTemplate>
      <UnauthenticatedTemplate>
        <div className="card form-horizontal mt-3" style={{ width: '55rem' }}>
          <div className="card-body">
            <h2 className="text-center text-primary card-title mb-2">
              Please Sign In
            </h2>
          </div>
        </div>
      </UnauthenticatedTemplate>
    </>
  )
}

export async function getServerSideProps(context) {
  const dencryptToken = getAccessToken(context.req)

  const { id, dashboard, fid } = context.params

  const bearer = `Bearer ${dencryptToken}`

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer,
    },
  }

  try {
    const resFormSubmission = await getFormSubmission(fid, config)
    console.log('form server', config, resFormSubmission)

    return {
      props: {
        id,
        fid,
        dashboard,
        savedData: resFormSubmission.data.formSubmissionData.data,
        initialFormSubmissionData:
          resFormSubmission.data.formSubmissionData.data,
      },
    }
  } catch (err) {
    console.error('Error fetching form submission:', err)
    return {
      props: {
        data: [],
        error: err?.message ?? 'Something Went Wrong !!!',
        token: !!dencryptToken,
      },
    }
  }
}
