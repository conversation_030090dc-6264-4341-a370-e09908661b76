import Image from 'next/image'
import DeleteIcon from '../../svg/metronic/delete.svg'
import PlusIcon from '../../svg/metronic/plus.svg'
import style from '../../pages/LMS/index.module.css'
import TextInput from '../UI/Input/TextInput/TextInput'
import TextareaInput from '../UI/Input/TextareaInput/TextareaInput'
import SelectInput from '../UI/Input/SelectInput/SelectInput'
import clsx from 'clsx'
import { useRef, useState } from 'react'
export default function Assignment({ questions, setQuestions }) {
  const courseOptions = [
    { label: 'A', value: 'A' },
    { label: 'B', value: 'B' },
    { label: 'C', value: 'C' },
    { label: 'D', value: 'D' },
  ]

  const questionRefs = useRef([])

  const handleAddQuestion = () => {
    setQuestions((prev) => {
      const newQuestions = [
        ...prev,
        {
          id: prev.length + 1,
          question: '',
          options: [
            { choiceText: '' },
            { choiceText: '' },
            { choiceText: '' },
            { choiceText: '' },
          ],
          answer: '',
        },
      ]
      return newQuestions
    })

    setTimeout(() => {
      const lastRef = questionRefs.current[questions.length]
      lastRef?.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }, 100)
  }

  const handleOnChange = (key, value, questionIndex, optionIndex) => {
    switch (key) {
      case 'answer':
        console.log('answer', value, questionIndex)
        setQuestions((prev) => {
          const updatedQuestions = [...prev]
          updatedQuestions[questionIndex].answer = value
          return updatedQuestions
        })
        break
      case 'question':
        setQuestions((prev) => {
          const updatedQuestions = [...prev]
          updatedQuestions[questionIndex].question = value
          return updatedQuestions
        })
        break
      case 'options':
        setQuestions((prev) => {
          const updatedQuestions = [...prev]
          updatedQuestions[questionIndex].options[optionIndex].choiceText =
            value
          return updatedQuestions
        })
        break
    }
  }

  return (
    <div className="flex flex-column gap-5">
      <div className={style.pageCard}>
        {/* <div className={clsx('justify-content-center', style.prospecTable)}>
          <span className="text-xl font-bold">Create Assessment</span>
        </div>
        <div
          className={clsx('py-6 flex flex-column gap-4', style.formSideSpace)}
        >
          <TextInput
            theme="metronic"
            label="Title of the Assessment"
            placeholder="Enter your title of the assessment"
          />
          <TextareaInput
            label="Description"
            placeholder="Enter your description"
            theme="metronic"
            rows={5}
            cols={30}
            height="5rem"
            containerHeight="auto"
            style={{ width: '100%', resize: 'none' }}
          />
          <div className="flex gap-4">
            <SelectInput
              theme="metronic"
              label="Display grade as"
              options={courseOptions}
              optionLabel="label"
              optionValue="value"
            />
            <SelectInput
              theme="metronic"
              label="Average percentage to pass the course"
              options={courseOptions}
              optionLabel="label"
              optionValue="value"
            />
          </div>
          <SelectInput
            theme="metronic"
            label="Assessment Type"
            options={courseOptions}
            optionLabel="label"
            optionValue="value"
          />
        </div> */}
        <h3 className={clsx(style.formSubTitle, style.formLeftSpace)}>
          {'Add Questionnaire'}
        </h3>
        {questions.map((question, qIndex) => (
          <div
            key={qIndex}
            ref={(el) => (questionRefs.current[qIndex] = el)}
            className={clsx('py-6 flex flex-column gap-4', style.formSideSpace)}
          >
            <div className="flex gap-4 align-items-center">
              <span
                className={style.orderBox}
                style={{ position: 'relative', bottom: '3px' }}
              >
                {qIndex + 1}
              </span>
              <TextInput
                theme="metronic"
                placeholder="Type your question here"
                value={question.question}
                onChange={(e) =>
                  handleOnChange('question', e.target.value, qIndex)
                }
              />
              {questions.length > 1 && (
                <Image
                  src={DeleteIcon}
                  alt="delete"
                  className="cursor-pointer"
                  onClick={() => {
                    setQuestions((prev) => prev.filter((_, i) => i !== qIndex))
                  }}
                />
              )}
              {qIndex === questions.length - 1 && (
                <Image
                  src={PlusIcon}
                  alt="add"
                  className="cursor-pointer"
                  onClick={handleAddQuestion}
                />
              )}
            </div>
            <div
              className={clsx('flex flex-column gap-3', style.optionSideSpace)}
            >
              <div className="grid">
                {question?.options?.map((option, index) => (
                  <div
                    key={index}
                    className="relative col-6  align-items-center"
                  >
                    <div
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer"
                      style={{ paddingLeft: '0.2rem', paddingTop: '0.1rem' }}
                    >
                      <span
                        className={style.orderBox}
                        style={{ position: 'relative' }}
                      >
                        {String.fromCharCode(65 + index)}
                      </span>
                    </div>
                    <TextInput
                      theme="metronic"
                      placeholder="enter your option here"
                      withOptions={true}
                      value={option.choiceText}
                      onChange={(e) =>
                        handleOnChange('options', e.target.value, qIndex, index)
                      }
                    />
                  </div>
                ))}
              </div>
              <TextInput
                theme="metronic"
                placeholder="Type your Answer here"
                value={question.answer}
                onChange={(e) =>
                  handleOnChange('answer', e.target.value, qIndex)
                }
              />
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}
