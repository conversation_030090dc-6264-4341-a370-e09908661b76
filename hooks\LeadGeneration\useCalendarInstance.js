import { useRef, useCallback } from 'react'

export const useCalendarInstance = () => {
  const calendarRef = useRef(null)
  const currentViewRef = useRef(null)

  const saveCurrentView = useCallback(() => {
    if (calendarRef.current) {
      const instance = calendarRef.current.getInstance()
      currentViewRef.current = {
        date: new Date(instance.getDate().getTime()), // Create a copy of the date
        view: instance.getViewName(),
        renderRange: instance.getRenderRange(), // Store render range
      }
    }
  }, [])

  const restoreView = useCallback(() => {
    if (calendarRef.current && currentViewRef.current) {
      const instance = calendarRef.current.getInstance()
      // First set the view type
      instance.changeView(currentViewRef.current.view)
      // Then set the exact date to maintain the same view position
      instance.setDate(currentViewRef.current.date)

      // Force a render update
      instance.render()
    }
  }, [])

  return {
    calendarRef,
    saveCurrentView,
    restoreView,
  }
}
