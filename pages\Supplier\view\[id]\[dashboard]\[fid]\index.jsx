import {
  getFormDefinition,
  getFormSubmission,
} from '../../../../../../api/apiCalls'
import { getAccessToken } from '../../../../../../utillites/getAccessToken'
import UnauthorizedPage from '../../../../../401'
import { Client } from '../../../../../PurchaseOrder/[id]'
import FormSubmissionView from '../../../../../view/[id]/[dashboard]/[fid]'

export default function Home({
  id,
  fid,
  dashboard,
  metadata,
  initialConditions,
  formDefinitionData,
  initialFormSubmission,
  initialFormSubmissionData,
  token,
  error,
}) {
  return (
    <Client>
      {error ? (
        <UnauthorizedPage token={token} error={error} />
      ) : (
        <FormSubmissionView
          id={id}
          fid={fid}
          dashboard={dashboard}
          metadata={metadata}
          initialConditions={initialConditions}
          formDefinitionData={formDefinitionData}
          initialFormSubmission={initialFormSubmission}
          initialFormSubmissionData={initialFormSubmissionData}
        />
      )}
    </Client>
  )
}

export async function getServerSideProps(context) {
  const dencryptToken = getAccessToken(context.req)
  const { id, dashboard, fid } = context.params

  const bearer = `Bearer ${dencryptToken}`

  const config = {
    headers: {
      ContentType: 'application/json',
      Authorization: bearer,
    },
  }

  try {
    const resFormDefinition = await getFormDefinition(id, config)
    const resFormSubmission = await getFormSubmission(fid, config)

    return {
      props: {
        id,
        fid,
        dashboard,
        metadata: resFormDefinition.data.metadata.metadata.form ?? {},
        initialConditions: resFormDefinition.data.metadata.conditions ?? {},
        savedData: resFormSubmission.data.formSubmissionData.data,
        initialFormSubmission: resFormSubmission.data.formSubmission,
        initialFormSubmissionData:
          resFormSubmission.data.formSubmissionData.data,
        formDefinitionData: resFormDefinition.data,
      },
    }
  } catch (err) {
    console.error(err)
    return {
      props: {
        data: [],
        error: err?.message ?? 'Something Went Wrong !!!',
        token: !!dencryptToken,
      },
    }
  }
}
