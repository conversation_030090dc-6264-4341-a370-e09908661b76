import { useQuery } from '@tanstack/react-query'
import { useEffect } from 'react'
import { useAcquireToken } from '../useAcquireToken'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const useFetchFormApprovalData = ({ fid, formSubmissionGuid }) => {
  console.log('fid', fid)
  const { acquireToken } = useAcquireToken()
  const {
    data: formSubmission,
    isLoading: formSubmissionLoading,
    refetch: refetchFormSubmission,
  } = useQuery({
    queryKey: ['formSubmission', formSubmissionGuid],
    queryFn: fetchFormSubmission,
    staleTime: 0,
    refetchInterval: false,
    refetchOnWindowFocus: false,
  })
  const {
    data: formSubmissionMaster,
    isLoading: formSubmissionMasterLoading,
    refetch: refetchFormSubmissionMaster,
  } = useQuery({
    queryKey: ['formSubmissionMaster', formSubmissionGuid],
    queryFn: fetchFormSubmissionMaster,
    staleTime: 0,
    enabled: !!(formSubmission && formSubmission?.formSubmissionMasterId),
  })

  useEffect(() => {
    if (
      formSubmission?.formSubmissionMasterId &&
      formSubmission?.formSubmissionMasterId != 0
    ) {
      refetchFormSubmissionMaster()
    }
  }, [formSubmission, refetchFormSubmissionMaster, refetchFormSubmission])

  async function fetchFormSubmission() {
    const { accessToken } = await acquireToken()
    const response = await fetch(`${api}FormSubmission/${fid}`, {
      headers: {
        Authorization: `Bearer ${accessToken}`,
      },
    })

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    let res = await response.json()
    return res.formSubmission ?? {}
  }

  async function fetchFormSubmissionMaster() {
    const { accessToken } = await acquireToken()
    const response = await fetch(
      `${api}FormSubmissionMaster/${formSubmission.formSubmissionMasterId}`,
      {
        headers: {
          Authorization: `Bearer ${accessToken}`,
        },
      }
    )

    if (!response.ok) {
      throw new Error(`API call failed: ${response.statusText}`)
    }

    let res = await response.json()

    if (res?.formSubmissions) {
      const minId = Math.min(
        ...res.formSubmissions.map((fs) => fs.formSubmissionId)
      )
      res.formSubmissions.sort((a, b) => {
        if (a.formSubmissionId === minId) return -1
        if (b.formSubmissionId === minId) return 1
        return b.formSubmissionId - a.formSubmissionId
      })
    }
    return res ?? {}
  }

  return { formSubmission, formSubmissionMaster, refetchFormSubmissionMaster }
}
