import CourseThumbnail from '../../svg/metronic/LMS_course_tn.svg'
import Image from 'next/image'
import Button from '../UI/Button/Button'
import { useState } from 'react'

const CourseCard = ({
  courseTitle,
  courseDescription,
  assignedDate,
  thumbnailUrl = null,
  buttonAction = () => {},
  hasLeadEnrolled = false,
}) => {
  const [isLoaded, setIsLoaded] = useState(false)
  return (
    <div
      style={{
        height: '30rem',
        width: '22rem',
        border: '1px solid rgba(81, 81, 81, 0.1)',
        borderRadius: '1.25rem',
      }}
      className="flex flex-column"
    >
      <div className="flex justify-content-center pt-4">
        <Image
          src={isLoaded ? thumbnailUrl : CourseThumbnail}
          alt="Course Card Background"
          width={295}
          height={245}
          onLoad={() => setIsLoaded(true)}
        />
      </div>
      <div
        className="flex flex-column gap-2"
        style={{
          padding: '1.875rem',
        }}
      >
        <div
          style={{
            fontFamily: 'Open Sans',
            fontWeight: 700,
            fontSize: '16px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textTransform: 'capitalize',
            color: 'rgba(81, 81, 81, 1)',
          }}
        >
          {courseTitle}
        </div>
        <div
          style={{
            fontFamily: 'Open Sans',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textTransform: 'capitalize',
            color: 'rgba(27, 132, 255, 1)',
            marginTop: '0.25rem',
          }}
        >
          {courseDescription}
        </div>
        <div
          style={{
            fontFamily: 'Open Sans',
            fontWeight: 500,
            fontSize: '16px',
            lineHeight: '100%',
            letterSpacing: '0%',
            textTransform: 'capitalize',
            color: 'rgba(81, 81, 81, 1)',
            marginTop: '0.25rem',
            height: '1.5rem',
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {assignedDate}
        </div>
      </div>
      <div className="flex justify-content-center">
        <Button
          label={hasLeadEnrolled ? 'Resume Course' : 'Enroll Now'}
          onClick={buttonAction}
          width={'200px'}
          theme="metronic"
        ></Button>
      </div>
    </div>
  )
}

export default CourseCard
