import crypto from 'crypto'

// Constants
const ALGORITHM = process.env.ALGORITHM || 'aes-256-gcm' // Encryption algorithm
const IV_LENGTH = 16 // Initialization vector length
const SALT_LENGTH = 16 // Salt length
const SECRET_KEY = process.env.SECRET // Must be 32 bytes for AES-256

// Derive a 32-byte key using SHA-256
const getKey = () => {
  return crypto.createHash('sha256').update(SECRET_KEY).digest()
}

// Encrypt Function
export function encryptToken(data) {
  const iv = crypto.randomBytes(IV_LENGTH) // Random IV
  const salt = crypto.randomBytes(SALT_LENGTH).toString('hex') // Random salt

  // Derive key using PBKDF2 (optional, for additional security)
  const derivedKey = crypto.pbkdf2Sync(getKey(), salt, 100000, 32, 'sha256')

  const cipher = crypto.createCipheriv(ALGORITHM, derivedKey, iv)
  let encrypted = cipher.update(data, 'utf8', 'hex')
  encrypted += cipher.final('hex')

  const authTag = cipher.getAuthTag().toString('hex')

  // Return encrypted data along with salt and IV
  return `${salt}.${iv.toString('hex')}.${authTag}.${encrypted}`
}

// Decrypt Function
export function decryptToken(encryptedData) {
  const [salt, iv, authTag, encrypted] = encryptedData.split('.')
  if (!salt || !iv || !authTag || !encrypted) {
    throw new Error('Invalid encrypted data format.')
  }

  const derivedKey = crypto.pbkdf2Sync(getKey(), salt, 100000, 32, 'sha256')

  const decipher = crypto.createDecipheriv(
    ALGORITHM,
    derivedKey,
    Buffer.from(iv, 'hex')
  )
  decipher.setAuthTag(Buffer.from(authTag, 'hex'))

  let decrypted = decipher.update(encrypted, 'hex', 'utf8')
  decrypted += decipher.final('utf8')

  return decrypted
}
