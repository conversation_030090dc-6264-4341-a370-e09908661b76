.bannerContainer {
  border: 1px solid #5151511a;
  border-radius: 1.25rem;
  padding-left: 1.25rem;
  padding-right: 1.25rem;
  height: 14rem;
}

.bannerBackground {
  background-size: 100%;
  background-position: bottom;
  background-repeat: no-repeat;
}

.userInfoContainer {
  padding-left: 1.25rem;
}

.userName {
  font-weight: 700;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0%;
  color: rgba(81, 81, 81, 1);
}

.userRole {
  font-weight: 700;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
  color: rgba(27, 132, 255, 1);
  vertical-align: middle;
}

.labelText {
  font-family: 'Open Sans';
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: rgba(81, 81, 81, 1);
}

.valueText {
  font-family: 'Open Sans';
  font-weight: 500;
  font-size: 14px;
  line-height: 28px;
  letter-spacing: 0%;
  vertical-align: middle;
  color: rgba(27, 132, 255, 1);
}

.spacedLabel {
  composes: labelText;
  padding-left: 1.25rem;
}

.welcomeText {
  font-family: 'Open Sans';
  font-weight: 400;
  font-size: 40px;
  line-height: 100%;
  letter-spacing: 0%;
  text-transform: capitalize;
  color: rgba(81, 81, 81, 1);
}

.prospecTable {
  background-color: #1b84ff1a;
  color: #515151;
  padding: 1rem;
  padding-top: 10px;
  padding-bottom: 10px;
  border-top-right-radius: 10px;
  border-top-left-radius: 10px;
  border: 1px solid #5151511a;
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: '3.75rem';
}

.search {
  flex: 1;
  border: 1px solid var(--primary-bg-darkCerulean-10) !important;
  border-radius: 10px;
  padding: 8px 12px;
  font-size: 15px;
  outline: none;
  background: var(--background-light);
  color: #515151;
  height: 45px;
  width: 16rem;
}

.searchIcon {
  color: #51515180;
  font-size: 18px;
  right: 16px;
  position: absolute;
  display: flex;
  align-items: center;
  left: auto;
  cursor: pointer;
}

.card {
  height: 30rem;
  width: 22rem;
  border: 1px solid rgba(81, 81, 81, 0.1);
  border-radius: 1.25rem;
}

.titleText {
  color: #1b84ff;
  text-decoration: underline;
  font-size: 16px;
  font-weight: 500;
}

.titleCard {
  background-color: #fffdef;
  border: 1px solid #ffde05;
  width: 100%;
  height: 3.75rem;
  border-radius: 10px;
}

.tabContainer {
  border-bottom: 1px solid #5151511a;
  display: flex;
  gap: 1.5rem;
  width: 100%;
}

.formTitle {
  font-weight: 600;
  font-size: 1.25rem;
  margin-bottom: 8px;
  color: #515151;
  background-color: #1b84ff1a;
  letter-spacing: -0.2px;
  font-weight: bold;
  width: 100%;
  text-align: flex-start;
  padding: 20px;
  border-top-right-radius: 20px;
  border-top-left-radius: 20px;
  border-bottom: 1px solid #5151511a;
}

.pageCard {
  width: 100%;
  background-color: #fff;
  border-radius: 20px;
  height: auto;
  border: 1px solid #5151511a;
}

.numText {
  color: #515151;
  font-size: 40px;
  font-weight: 700;
}

.courseText {
  color: #515151;
  font-size: 16px;
  font-weight: 500;
}

.assessmentContainer {
  width: 100%;
  height: auto;
  border: 1px solid #1b84ff;
  background-color: #1b84ff0d;
  border-radius: 20px;
  padding: 1rem;
}

.fontText {
  color: #515151;
}

.loadLine {
  background-color: #1b84ff4d;
  border-radius: 10px;
  width: 100%;
  height: 10px;
}

.activeLoadLine {
  background-color: #1b84ff;
  border-radius: 10px;
  width: 100%;
  height: 10px;
}

.link {
  color: #1b84ff;
  text-decoration: underline;
  cursor: pointer;
  font-weight: 700;
}

.courseTitle {
  font-family: 'Open Sans';
  font-weight: 700;
  font-size: 16px;
  line-height: 100%;
  color: rgba(81, 81, 81, 1);
}

.courseDescription {
  font-family: 'Open Sans';
  font-weight: 400;
  font-size: 14px;
  line-height: 100%;
  color: rgba(81, 81, 81, 1);
  padding-top: 0.25rem;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.courseAuthor {
  font-family: 'Open Sans';
  font-weight: 400;
  font-style: italic;
  font-size: 14px;
  line-height: 100%;
  padding-top: 0.25rem;
  color: rgba(81, 81, 81, 1);
}

.searchIcon {
  top: 22px;
}

.searchContainer {
  display: flex;
  width: 22vw;
}

.bannerTitle {
  position: absolute;
  left: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: bold;
}

.bannerText {
  position: absolute;
  left: 1.5rem;
  color: white;
  font-size: 2rem;
  font-weight: bold;
}
.imageContainer {
  width: 50%;
  text-align: center;
  justify-content: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
}
.imageBoxYellow {
  position: relative;
  height: 10rem;
  width: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.imageBoxYellow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../../svg/metronic/polygon_yellow.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transform-origin: center;
  transform: rotate(0deg);
  transition: transform 0.6s ease;
  z-index: 1;
}
.boxYellow {
  position: relative;
  height: 10rem;
  width: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.boxYellow::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../../svg/metronic/polygon_yellow.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transform-origin: center;
  transform: rotate(0deg);
  transition: transform 0.6s ease;
}

.boxYellow:hover::before {
  transform: rotate(20deg) scale(1.1);
}

.boxBlue {
  position: relative;
  height: 10rem;
  width: 10rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  overflow: hidden;
  transition: transform 0.3s ease;
}

.boxBlue::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(../../svg/metronic/polygon_blue.svg);
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
  transform-origin: center;
  transform: rotate(0deg);
  transition: transform 0.3s ease;
}
.boxBlue:hover::before {
  transform: rotate(20deg) scale(1.1);
}

.textBlue {
  font-size: 16px;
  color: #1b84ff;
}
.bar {
  color: #d9d9d9;
  transform: rotate(90deg);
}
