import Image from 'next/image'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import Button from '../../../components/UI/Button/Button'
import style from '../../../pages/LMS/index.module.css'
import { useContext, useEffect, useRef, useState } from 'react'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Modal from '../../../components/UI/Modal/Modal'
import clsx from 'clsx'
import Delete from '../../../svg/metronic/delete.svg'
import Edit from '../../../svg/metronic/pencil_line.svg'
import PdfThumbnail from '../../../svg/metronic/LMS_pdf.svg'
import VideoThumbnail from '../../../svg/metronic/video_icon.svg'
import { useCourse } from '../../../hooks/LMS/Course/useCourse'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import { Tab } from '../../../components/UI/Tabs/Tabs'
import { useChapterTabs } from '../../../hooks/LMS/Course/useChapterTabs'
import CreateChapterModal from '../../../components/LMS/Modals/CreateChapterModal'
import CreateModuleModal from '../../../components/LMS/Modals/CreateModuleModal'
import useDeleteChapter from '../../../hooks/LMS/Course/useDeleteChapter'
import Assignment from '../../../components/LMS/Assignment'
import ManageStudent from '../../../components/LMS/ManageStudent'
import ManageGrades from '../../../components/LMS/ManageGrades'
import Settings from '../../../components/LMS/Settings'
import { useAssessment } from '../../../hooks/LMS/Course/useAssessment'
import { useCreateCourse } from '../../../hooks/LMS/Course/useCreateCourse'
import { useCourseType } from '../../../hooks/LMS/Course/useCourseType'
import { useRouter } from 'next/router'
import { Toast } from 'primereact/toast'
import ManageStudentModal from '../../../components/LMS/Modals/ManageStudentModal'
import Reject from '../../../svg/metronic/reject.svg'
import Success from '../../../svg/metronic/success.svg'
import Close from '../../../svg/metronic/close_toast.svg'
import { EmptyDataTemplate } from '../../../components/LMS/LMSTableComponents'

const ContentList = ({
  title,
  items,
  setItems,
  author,
  courseTitle,
  fetchAvailableCourseById,
  toastNew,
}) => {
  const [dragIndex, setDragIndex] = useState(null)
  const toast = useRef(null)
  const router = useRouter()
  const { handleDeleteChapter, loading } = useDeleteChapter(toast)

  const handleDragStart = (index) => setDragIndex(index)
  const handleDrop = (dropIndex) => {
    if (dragIndex === null || dragIndex === dropIndex) return
    const updated = [...items]
    const dragged = updated.splice(dragIndex, 1)[0]
    updated.splice(dropIndex, 0, dragged)
    setItems(updated)
    setDragIndex(null)
  }
  const handleDeleteCallback = () => {
    toastNew.current?.show({
      severity: 'success',
      summary: 'Success',
      detail: `Module Deleted Successfully!`,
    })
    fetchAvailableCourseById()
  }
  return (
    <div className={style.pageCard}>
      <div className={style.prospecTable}>
        <span className="text-xl font-bold">{title}</span>
      </div>
      <div className="m-4">
        {items.map((data, index) => (
          <div
            className="flex align-items-center"
            key={`${title}-${index}`}
            draggable
            onDragStart={() => handleDragStart(index)}
            onDragOver={(e) => e.preventDefault()}
            onDrop={() => handleDrop(index)}
            style={{ cursor: 'grab' }}
          >
            <div>{index + 1}</div>
            <div className={clsx('mx-5 my-3 cursor-pointer', style.pageCard)}>
              <div className="flex justify-content-between align-items-center p-5">
                <div className="flex align-items-center gap-4">
                  <i className={clsx('pi pi-bars', style.bar)}></i>
                  <Image
                    src={
                      data.fileType === 'application/pdf'
                        ? PdfThumbnail
                        : VideoThumbnail
                    }
                    alt="pdf-thumbnail"
                  />
                  <div className="flex flex-column gap-2">
                    <div className="flex gap-4 align-items-center">
                      <span className={style.courseTitle}>
                        {data?.chapterTitle}
                      </span>
                      <Image src={Edit} alt="edit" />
                    </div>
                    <span className={clsx('text-sm', style.fontText)}>
                      {data.overview}
                    </span>
                    <span className={style.courseAuthor}>By {author}</span>
                  </div>
                </div>
                <Button
                  label="view"
                  theme="metronic"
                  variant="outline"
                  width="150px"
                  onClick={() =>
                    router.push({
                      pathname: `/InstructorLMS/Courses/${data.courseId}/${data.id}`,
                      query: {
                        courseTitle: courseTitle,
                      },
                    })
                  }
                />
              </div>
            </div>
            <Image
              src={Delete}
              alt="delete"
              onClick={() => handleDeleteChapter(data.id, handleDeleteCallback)}
              className="cursor-pointer"
            />
          </div>
        ))}
      </div>
      <ConditionalDisplay condition={items?.length === 0}>
        <EmptyDataTemplate message="No Chapters Added" />
      </ConditionalDisplay>
    </div>
  )
}

export default function NewCourse({ id, courseTitle }) {
  const router = useRouter()
  const toast = useRef(null)
  const [showCreateChapterModal, setShowCreateChapterModal] = useState(false)
  const [showCreateModuleModal, setShowCreateModuleModal] = useState(false)
  const [showManageStudentsModal, setShowManageStudentsModal] = useState(false)

  const { userID: userId } = useContext(UserProfileContext)
  const { fetchAvailableCourseById, loading, course, setCourse } = useCourse(
    id,
    userId
  )
  const { currentTab, setCurrentTab, tabs } = useChapterTabs()
  const { courseTypes, fetchCourseTypes } = useCourseType()

  const {
    fetchAssessmentWithAnswers,
    questionAnswers,
    createAssessment,
    updateAssessment,
  } = useAssessment()

  const { updateCourse } = useCreateCourse()

  useEffect(() => {
    if (id) {
      fetchAssessmentWithAnswers(id)
    }
  }, [id])

  useEffect(() => {
    if (questionAnswers?.length > 0) {
      const transformedQuestions = questionAnswers?.map((item) => ({
        id: item.id,
        question: item.question,
        options: item.choices,
        answer: item.choices
          ?.filter((choice) => item?.answers?.includes(choice.id))
          .map((choice) => choice.choiceText)
          .join(','),
      }))
      setQuestions(transformedQuestions)
    }
  }, [questionAnswers])

  useEffect(() => {
    fetchCourseTypes()
  }, [])

  const [questions, setQuestions] = useState([
    {
      id: 1,
      question: '',
      options: [
        { choiceText: '' },
        { choiceText: '' },
        { choiceText: '' },
        { choiceText: '' },
      ],
      answer: '',
    },
  ])

  useEffect(() => {
    if (id && userId) {
      fetchAvailableCourseById()
    }
  }, [id, userId])

  useEffect(() => {
    if (course) {
      setChapterData(course.chapters)
    }
  }, [course])

  const [chapterData, setChapterData] = useState([])
  // const [quizData, setQuizData] = useState([
  //   {
  //     title: 'Quiz 1: WordPress Basics',
  //     overview: 'Test your knowledge of basic WordPress concepts.',
  //     createdUser: 'Jane Smith',
  //   },
  //   {
  //     title: 'Quiz 2: Themes and Plugins',
  //     overview: 'A quiz on using and managing themes and plugins.',
  //     createdUser: 'Jane Smith',
  //   },
  // ])

  const publishCourse = () => {
    const requestBody = {
      courseId: course?.id,
      title: course?.title,
      overview: course?.overview,
      courseTypeId: course?.courseTypeId,
      thumbnailType: course?.thumbnailType,
      thumbnailName: course?.thumbnailName,
      thumbnailSize: course?.thumbnailSize,
      hasPublished: true,
      availableToAll: course?.availableToAll,
      availableToLeadIds: course?.availableToLeadUserProfileIds,
      availableFrom: course?.availableFrom,
      availableTo: course?.availableTo,
    }

    updateCourse(requestBody, () => {
      toast.current.show({
        severity: 'success',
        summary: 'Success',
        detail: `Course Published Successfully.`,
        icon: <Image src={Success} alt="success" />,
        closeIcon: <Image src={Close} alt="close" />,
      })
      router.back()
    })
  }

  const handleNextStep = () => {
    switch (currentTab) {
      case 0:
        setCurrentTab(currentTab + 1)
        break
      case 1:
        setCurrentTab(currentTab + 1)
        break
      case 2:
        setCurrentTab(currentTab + 1)
        break
      case 3:
        setCurrentTab(currentTab + 1)
        break
      case 4:
        publishCourse()
        break
    }
  }

  const createOrUpdateAssessment = () => {
    if (questionAnswers?.length > 0) {
      const requestBody = questions.map((question) => ({
        courseAssessmentId: question.options[0].courseAssessmentId,
        courseId: parseInt(id),
        question: question.question,
        updatedBy: userId,
        answers: question.answer.split(','),
        choiceType: 1,
        choices: question.options.map((option) => ({
          choiceText: option.choiceText,
          courseAssessmentChoiceId: option.id,
        })),
      }))
      console.log('requestBody', questions)
      updateAssessment(requestBody, () => {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Assessment Updated Successfully.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />,
        })
      })
    } else {
      const requestBody = questions.map((question) => ({
        courseId: parseInt(id),
        question: question.question,
        createdBy: userId,
        answers: question.answer.split(','),
        choiceType: 1,
        choices: question.options.map((option) => ({
          choiceText: option.choiceText,
        })),
      }))
      createAssessment(requestBody, () => {
        toast.current.show({
          severity: 'success',
          summary: 'Success',
          detail: `Assessment Created Successfully.`,
          icon: <Image src={Success} alt="success" />,
          closeIcon: <Image src={Close} alt="close" />,
        })
      })
    }
  }

  const updateCourseSettings = () => {
    //updateCourse()
  }

  const handleAddAction = () => {
    switch (currentTab) {
      case 0:
        setShowCreateChapterModal(true)
        break
      case 1:
        createOrUpdateAssessment()
        break
      case 2:
        setShowManageStudentsModal(true)
        break
      case 3:
        break
      case 4:
        updateCourseSettings()
        break
    }
  }

  return (
    <PageContainer theme="metronic">
      <Toast ref={toast} />
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image
            src={Backarrow}
            alt="Back"
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <BreadCrumbs
            title="Create New Course"
            breadcrumbItems={[
              { label: 'Create New Course' },
              { label: tabs.find((tab) => tab.value === currentTab)?.title },
            ]}
            theme="metronic"
          />

          <div className="flex gap-3">
            <Button
              label={
                currentTab === 2
                  ? 'Manage Students'
                  : currentTab === 4
                  ? 'Update'
                  : 'Add'
              }
              variant="outline"
              theme="metronic"
              width={currentTab === 2 ? '180px' : '150px'}
              onClick={handleAddAction}
            />
            <Button
              label={currentTab === 4 ? 'Publish' : 'Next'}
              theme="metronic"
              width="150px"
              onClick={handleNextStep}
            />
          </div>
        </div>
        <div
          className={clsx('flex gap-4', style.tabContainer)}
          style={{ height: '2.5rem' }}
        >
          {tabs.map((tab) => {
            if (tab.value === 3 && !course?.hasPublished) {
              return null
            }
            return (
              <Tab
                key={tab.value}
                title={tab.title}
                value={tab.value}
                display={true}
                isActive={currentTab === tab.value}
                handleClick={(e, value) => {
                  setCurrentTab(value)
                }}
                theme="metronic"
              />
            )
          })}
        </div>
        <ConditionalDisplay condition={currentTab === 0}>
          <ContentList
            title="Chapters"
            items={chapterData}
            setItems={setChapterData}
            author={course?.createdUser}
            courseTitle={courseTitle}
            toastNew={toast}
            fetchAvailableCourseById={fetchAvailableCourseById}
          />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 1}>
          <Assignment questions={questions} setQuestions={setQuestions} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 2}>
          <ManageStudent courseId={id} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 3}>
          <ManageGrades courseId={id} />
        </ConditionalDisplay>
        <ConditionalDisplay condition={currentTab === 4}>
          <Settings course={course} courseTypes={courseTypes} />
        </ConditionalDisplay>

        {/* <ContentList title="Quizzes" items={quizData} setItems={setQuizData} /> */}

        <Modal
          visible={showCreateModuleModal}
          theme="metronic"
          onHide={() => setShowCreateModuleModal(false)}
          header="Add New Module/Assignment"
          style={{ background: '#fff', width: '40vw' }}
        >
          <CreateModuleModal
            setShowCreateChapterModal={setShowCreateChapterModal}
            setShowCreateModuleModal={setShowCreateModuleModal}
          ></CreateModuleModal>
        </Modal>

        <Modal
          visible={showCreateChapterModal}
          theme="metronic"
          onHide={() => setShowCreateChapterModal(false)}
          header="Add New Module"
          style={{ background: '#fff', width: '40vw' }}
        >
          <CreateChapterModal
            setShowCreateChapterModal={setShowCreateChapterModal}
            order={chapterData.length + 1}
            courseId={id}
            toast={toast}
            fetchAvailableCourseById={fetchAvailableCourseById}
          />
        </Modal>
        <Modal
          visible={showManageStudentsModal}
          theme="metronic"
          onHide={() => setShowManageStudentsModal(false)}
          header="Add New Module"
          style={{ background: '#fff', width: '40vw' }}
        >
          <ManageStudentModal
            course={course}
            setCourse={setCourse}
            updateCourse={updateCourse}
            loading={loading}
            setShowManageStudentsModal={setShowManageStudentsModal}
            toast={toast}
          />
        </Modal>
      </div>
    </PageContainer>
  )
}

export async function getServerSideProps(context) {
  const { params } = context
  const { id } = params
  const { courseTitle } = context.query

  return {
    props: {
      id,
      courseTitle: courseTitle,
    },
  }
}
