import Image from 'next/image'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import Button from '../../../components/UI/Button/Button'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import ProfileBanner from '../../../components/LMS/ProfileBanner'
import { useContext, useEffect, useRef, useState } from 'react'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import Modal from '../../../components/UI/Modal/Modal'
import {
  ActionTemplate,
  CourseDescriptionTemplate,
  CourseTypeTemplate,
  EmptyDataTemplate,
  TableHeader,
} from '../../../components/LMS/LMSTableComponents'
import CreateCourseModal from '../../../components/LMS/Modals/CreateCourseModal'
import { useCourseType } from '../../../hooks/LMS/Course/useCourseType'
import { Toast } from 'primereact/toast'
import { useAvailableCourses } from '../../../hooks/LMS/AvailableCourses/useAvailableCourses'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'
import { useRouter } from 'next/router'

export default function CourseDashboard() {
  const [createModal, setCreateModal] = useState(false)
  const { courseTypes, fetchCourseTypes } = useCourseType()
  const toast = useRef(null)
  const { userID: userId } = useContext(UserProfileContext)
  const router = useRouter()

  const publishedCourses = useAvailableCourses(5, true)

  const unPublishedCourses = useAvailableCourses(5, false)

  useEffect(() => {
    fetchCourseTypes()
    if (userId) {
      publishedCourses.fetchCourses(0, userId)
      unPublishedCourses.fetchCourses(0, userId)
    }
  }, [userId])

  const mockColumns = (type) => {
    switch (type) {
      case 'publish':
        return [
          {
            field: 'number',
            header: 'No.',
            body: <CourseTypeTemplate />,
            style: { width: '10%' },
          },
          {
            field: 'lesson',
            header: 'Lesson',
            body: (rowData) => (
              <CourseDescriptionTemplate
                course={rowData}
                showDescription={true}
              />
            ),
          },
          {
            field: 'status',
            header: 'Status',
            body: (rowData) => (
              <ActionTemplate
                rowData={rowData}
                actionLabel={'View'}
                onClick={() => router.push(`/LMS/Instructor/${rowData?.id}`)}
              />
            ),
            style: { width: '10%' },
          },
        ]
      case 'unpublish':
        return [
          {
            field: 'number',
            header: 'No.',
            body: <CourseTypeTemplate />,
            style: { width: '10%' },
          },
          {
            field: 'lesson',
            header: 'Quiz',
            body: (rowData) => (
              <CourseDescriptionTemplate
                course={rowData}
                showDescription={true}
              />
            ),
            style: { width: '80%' },
          },
          {
            field: 'status',
            header: 'Status',
            body: (rowData) => (
              <ActionTemplate
                rowData={rowData}
                actionLabel={'View'}
                primary={true}
                primaryLabel={'Publish'}
                onClick={() => router.push(`/LMS/Instructor/${rowData?.id}`)}
                onClickPrimary={
                  // TODO: Implement publish functionality
                  () => {}
                }
              />
            ),
            style: { width: '10%' },
          },
        ]
      default:
        return []
    }
  }
  return (
    <PageContainer theme="metronic">
      <Toast ref={toast} />
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Dashboard"
            breadcrumbItems={[{ label: 'Dashboard' }]}
            theme="metronic"
          />
          <Button
            label="Create New Course"
            theme="metronic"
            width="250px"
            onClick={() => setCreateModal(true)}
          />
        </div>
        <ProfileBanner />
        <div>
          {[
            {
              title: 'Pubished Courses',
              type: 'publish',
              emptyMessage: 'You have no courses enrolled at the moment',
              data: publishedCourses,
            },
            {
              title: 'Unpublished Courses',
              type: 'unpublish',
              emptyMessage: 'You have no pending quizzes at the moment',
              data: unPublishedCourses,
            },
          ].map(({ title, data, type, emptyMessage }, index) => (
            <div
              style={{
                marginTop: '1.25rem',
                marginBottom: '1.25rem',
              }}
              key={index}
            >
              <DataTable
                header={() => <TableHeader text={title} />}
                className="custom-lead"
                style={{ cursor: 'pointer' }}
                showHeaders={false}
                value={data.courses}
                paginator={data.totalCount > 0}
                rows={data.lazyParams.rows}
                totalRecords={data.totalCount}
                lazy
                first={data.lazyParams.first}
                onPage={data.onPage}
                onSort={data.onSort}
                sortField={data.lazyParams.sortField}
                sortOrder={data.lazyParams.sortOrder}
                emptyMessage={() => (
                  <EmptyDataTemplate message={emptyMessage} />
                )}
                loading={data.loading}
              >
                {mockColumns(type).map((col, index) => (
                  <Column key={index} {...col} />
                ))}
              </DataTable>
            </div>
          ))}
        </div>
        <ConditionalDisplay condition={createModal}>
          <Modal
            visible={createModal}
            theme="metronic"
            onHide={() => setCreateModal(false)}
            header="Create New Course"
            style={{ background: '#fff', width: '40vw' }}
          >
            <CreateCourseModal
              setCreateModal={setCreateModal}
              courseTypes={courseTypes}
              toast={toast}
            />
          </Modal>
        </ConditionalDisplay>
      </div>
    </PageContainer>
  )
}
