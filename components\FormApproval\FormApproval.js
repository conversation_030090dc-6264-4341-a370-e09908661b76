// Library components/hooks
import { useMemo, useState } from 'react'
import { useRouter } from 'next/router'
import { Toast } from 'primereact/toast'

// Custom components/hooks
import PageCountDisplay from '../../components/UI/PageCountDisplay/PageCountDisplay'
import { useConcatenate } from '../../hooks/useConcatenate'
import { useCondition } from '../../hooks/FormBuilder/conditionHooks'
import { usePreventSubmit } from '../../hooks/usePreventSubmit'
import { useInputs } from '../../hooks/useInput'
import { useValidation } from '../../hooks/useValidation'
import { useGetFormPermissions } from '../../hooks/useGetFormPermissions'
import { useUpdateMetadata } from '../../hooks/useUpdateMetadata'
import { useSubmitFormValidation } from '../../hooks/useSubmitFormValidation'
import { ConditionalDisplay } from '../../components/UI/ConditionalDisplay/ConditionalDisplay'

// Form Approval Components
import { FormContainer } from '../../components/FormApproval/FormContainer/FormContainer'
import { PageCountDisplayContainer } from '../../components/UI/PageCountDisplayContainer/PageCountDisplayContainer'
import { RevisionOptionsDropdown } from '../../components/FormApproval/RevisionOptionsDropdown/RevisionOptionsDropdown'
import { SaveChanges } from '../../components/FormApproval/SaveChanges/SaveChanges'

import { SaveChangesContainer } from '../../components/UI/SaveChangesContainer/SaveChangesContainer'
import { ViewForm } from '../../components/FormApproval/ViewForm/ViewForm'

// Form Approval Hooks
import { useFetchFormApprovalData } from '../../hooks/FormApproval/useFetchFormApprovalData'
import { useFormApprovalActions } from '../../hooks/FormApproval/useFormApprovalActions'
import { useGetFormApprovalRevision } from '../../hooks/FormApproval/useGetFormApprovalRevision'
import { useSaveFormApprovalChanges } from '../../hooks/FormApproval/useSaveFormApprovalChanges'
import { useDisableFormApproval } from '../../hooks/FormApproval/useDisableFormApproval'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export default function FormApproval({
  fid,
  metadata,
  conditions,
  formDefinitionData,
  initialFormSubmissionData,
}) {
  const [formSubmissionData, setFormSubmissionData] = useState(
    initialFormSubmissionData
  )
  const [currentPage, setCurrentPage] = useState(0)

  const router = useRouter()
  const {
    inputs,
    setInputs,
    handleInputChange,
    files,
    assignValuesNested,
    setFiles,
  } = useInputs({ initialValues: formSubmissionData })
  const { checkSamePersonForSignature } = usePreventSubmit({
    metadata,
    inputs,
    setInputs,
    files,
    setFiles,
  })
  const { checkErrors } = useSubmitFormValidation({
    metadata,
    inputs,
    setInputs,
    setFiles,
    files,
  })
  const { getInputsForConcat } = useConcatenate({ metadata, inputs })
  const { currentApproverFormPermissions } = useGetFormPermissions({
    formSubmissionId: fid,
  })

  console.log('fid formApproval', fid)
  const { formSubmission, formSubmissionMaster, refetchFormSubmissionMaster } =
    useFetchFormApprovalData({ fid, formSubmissionGuid: router.query.fid })

  const recallStatus = formSubmission?.status

  const solutionId = formDefinitionData?.formDefinitionMaster?.solution?.id
  const totalPagesArray =
    Object.keys(metadata ?? {}).length > 0
      ? [
          ...new Set(
            Object.keys(metadata).map((key) => metadata[key].pageNumber)
          ),
        ]
      : [0]

  const { saveChangesDialog, setSaveChangesDialog, saveChangesCondition } =
    useSaveFormApprovalChanges({
      currentApproverFormPermissions,
      finalizedForm: formSubmission?.isFinalized,
    })
  const { formIsDisabled, saveButtonDisabled } = useDisableFormApproval()
  const { errors, validationMapper } = useValidation({
    metadata,
    inputs,
    authorName: formSubmission?.userFullLegalName,
  })
  const {
    selectedRevision,
    setSelectedRevision,
    revisionHistoryOptions,
    handleRevisionChange,
    isActiveRevision,
  } = useGetFormApprovalRevision({
    formSubmissionMaster,
    formSubmission,
    setFormSubmissionData,
    setInputs,
  })
  const { updateFormSubmission, submitLoading, formApprovalToast } =
    useFormApprovalActions({
      fid,
      formSubmission,
      inputs,
      files,
      checkErrors,
      checkSamePersonForSignature,
      refetchFormSubmissionMaster,
      setSaveChangesDialog,
      setSelectedRevision,
      getInputsForConcat,
      solutionId,
      metadata,
    })
  const { getUpdatedMetadata, getMetadataWithPermissions } = useUpdateMetadata({
    conditions,
    metadata,
    formPermissions: currentApproverFormPermissions,
    validationMapper,
    inputs,
    currentPage,
  })
  const { pageData, objectKeysArray, pageBreaks } = getUpdatedMetadata()

  const submissionData = useMemo(
    () => ({
      errors,
      fromSaveChanges: false,
      metadata,
      inputs,
    }),
    [inputs, errors]
  )

  return (
    <>
      <FormContainer>
        <ViewForm
          formHeader={formSubmission?.formDefinition?.name}
          metadata={metadata}
          inputs={inputs}
          handleInputChange={handleInputChange}
          errors={errors}
          assignValuesNested={assignValuesNested}
          formDefinitionData={formDefinitionData}
          formSubmission={formSubmission}
          formSubmissionData={formSubmissionData}
          disableForm={() =>
            formIsDisabled(formSubmission) || isActiveRevision === false
          } // Disables form when users are viewing a previous revision
          currentPage={currentPage}
          setCurrentPage={setCurrentPage}
          files={files}
          pageData={pageData}
          objectKeysArray={objectKeysArray}
          updatedMetadataWithPermissions={metadata}
          setInputs={setInputs}
          isApprovalPage={true}
          totalPagesArray={totalPagesArray}
          pageBreaks={pageBreaks}
          revisionDropdown={
            <RevisionOptionsDropdown
              formSubmissionMaster={formSubmissionMaster}
              revisionHistoryOptions={revisionHistoryOptions}
              selectedRevision={selectedRevision}
              handleRevisionChange={handleRevisionChange}
            />
          }
        />
        <ConditionalDisplay
          condition={!formDefinitionData?.pageDisplayOption === 1}
        >
          <PageCountDisplayContainer>
            <PageCountDisplay
              pageNumber={currentPage}
              totalPagesLength={totalPagesArray.length}
              footer={formDefinitionData?.footer ?? ''}
            />
          </PageCountDisplayContainer>
        </ConditionalDisplay>
        <SaveChangesContainer>
          <SaveChanges
            saveChangesCondition={saveChangesCondition}
            saveButtonDisabled={() =>
              saveButtonDisabled(
                formSubmission,
                inputs,
                files,
                initialFormSubmissionData
              )
            }
            saveChangesDialog={saveChangesDialog}
            setSaveChangesDialog={setSaveChangesDialog}
            updateFormSubmission={updateFormSubmission}
            updateFormInfo={submissionData}
            submitLoading={submitLoading}
            isActiveRevision={isActiveRevision}
            recallStatus={recallStatus}
          />
        </SaveChangesContainer>
      </FormContainer>
      <Toast ref={formApprovalToast} />
    </>
  )
}
