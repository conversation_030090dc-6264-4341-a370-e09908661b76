import Image from 'next/image'
import Button from '../UI/Button/Button'
import PdfThumbnail from '../../svg/metronic/LMS_pdf.svg'
import videoThumbnail from '../../svg/metronic/video_icon.svg'
import EmptyDataState from '../../svg/metronic/LMS_empty_data_state.svg'
import styles from '../../pages/LMS/index.module.css'
import { ConditionalDisplay } from '../UI/ConditionalDisplay/ConditionalDisplay'

export const TableHeader = ({ text }) => (
  <div className={styles.prospecTable}>
    <span className="text-xl font-bold">{text}</span>
    {/* <span
            className="p-input-icon-left"
            style={{
              display: "flex",
              flexDirection: "row-reverse",
            }}
          >
            <i
              className={clsx("pi pi-search", styles.searchIcon)}
              style={{ top: "22px" }}
            />
            <InputText placeholder="Search" className={styles.search} />
          </span> */}
  </div>
)

export const CourseTypeTemplate = () => (
  <div className="flex gap-4">
    <Image src={PdfThumbnail} alt="PDF Thumbnail" />
    <Image src={videoThumbnail} alt="Video Thumbnail" />
  </div>
)

export const EmptyDataTemplate = ({ message }) => (
  <div className="flex flex-column align-items-center my-8 gap-2">
    <Image src={EmptyDataState} alt="Empty Data State" />
    <span className="text-center">{message}</span>
  </div>
)

export const CourseDescriptionTemplate = ({
  course,
  showDescription = false,
}) => (
  <div className="flex flex-column gap-2">
    <div className={styles.courseTitle}>{course?.title}</div>
    {showDescription && (
      <div className={styles.courseDescription}>{course?.overview}</div>
    )}
    <div className={styles.courseAuthor}>{`by ${course?.createdUser}`}</div>
  </div>
)

export const ActionTemplate = ({
  rowData,
  actionLabel,
  onClick,
  onClickPrimary,
  primary,
  primaryLabel,
}) => (
  <div className="flex gap-4">
    <Button
      variant="outline"
      label={actionLabel}
      onClick={() => onClick(rowData)}
      width="13rem"
      theme="metronic"
    />
    <ConditionalDisplay condition={primary}>
      <Button
        label={primaryLabel}
        onClick={() => onClickPrimary(rowData)}
        width="13rem"
        theme="metronic"
      />
    </ConditionalDisplay>
  </div>
)
