import Image from "next/image";
import { PageContainer } from "../../../../components/UI/Page/PageContainer/PageContainer";
import BreadCrumbs from "../../../../components/UI/BreadCrumbs/BreadCrumbs";
import Backarrow from "../../../../svg/metronic/back_metronic.svg";
import Button from "../../../../components/UI/Button/Button";
import style from "../../../../pages/LMS/index.module.css"
import { useState } from "react";
import { ConditionalDisplay } from "../../../../components/UI/ConditionalDisplay/ConditionalDisplay";
import Modal from "../../../../components/UI/Modal/Modal";
import YellowPlus from "../../../../svg/metronic/yellow_plus.svg"
import BluePlus from "../../../../svg/metronic/blue_plus.svg"
import clsx from "clsx";
import Delete from '../../../../svg/metronic/delete.svg'
import Edit from "../../../../svg/metronic/pencil_line.svg";
import PdfThumbnail from "../../../../svg/metronic/LMS_pdf.svg";

const ContentList = ({ title, items, setItems }) => {
  const [dragIndex, setDragIndex] = useState(null);

  const handleDragStart = (index) => setDragIndex(index);
  const handleDrop = (dropIndex) => {
    if (dragIndex === null || dragIndex === dropIndex) return;
    const updated = [...items];
    const dragged = updated.splice(dragIndex, 1)[0];
    updated.splice(dropIndex, 0, dragged);
    setItems(updated);
    setDragIndex(null);
  };

  return (
    <div className={style.pageCard}>
      <div className={style.prospecTable}>
        <span className="text-xl font-bold">{title}</span>
      </div>
      <div className="m-4">
        {items.map((data, index) => (
          <div
            className="flex align-items-center"
            key={`${title}-${index}`}
            draggable
            onDragStart={() => handleDragStart(index)}
            onDragOver={(e) => e.preventDefault()}
            onDrop={() => handleDrop(index)}
            style={{ cursor: "grab" }}
          >
            <div>{index + 1}</div>
            <div className={clsx("mx-5 my-3 cursor-pointer", style.pageCard)}>
              <div className="flex justify-content-between align-items-center p-5">
                <div className="flex align-items-center gap-4">
                  <i className={clsx("pi pi-bars", style.bar)}></i>
                  <Image src={PdfThumbnail} alt="pdf-thumbnail" />
                  <div className="flex flex-column gap-2">
                    <div className="flex gap-4 align-items-center">
                      <span className={style.courseTitle}>{data.title}</span>
                      <Image src={Edit} alt="edit" />
                    </div>
                    <span className={clsx("text-sm", style.fontText)}>{data.overview}</span>
                    <span className={style.courseAuthor}>By {data.createdUser}</span>
                  </div>
                </div>
                <Button label="view" theme="metronic" variant="outline" width="150px" />
              </div>
            </div>
            <Image src={Delete} alt="delete" />
          </div>
        ))}
      </div>
    </div>
  );
};


export default function NewCourse() {
  const [createModal, setCreateModal] = useState(false);


  const [chapterData, setChapterData] = useState([
    {
      title: "WordPress Basics",
      overview: "Learn how to build a website from scratch using WordPress.",
      createdUser: "John Doe",
    },
    {
      title: "Build a Free Website with WordPress-22",
      overview: "Learn how to build a website from scratch using WordPress.",
      createdUser: "John Doe",
    },
    {
      title: "Build a Free Website with WordPress-33",
      overview: "Learn how to build a website from scratch using WordPress.",
      createdUser: "John Doe",
    }
  ]);
  const [quizData, setQuizData] = useState([
    {
      title: "Quiz 1: WordPress Basics",
      overview: "Test your knowledge of basic WordPress concepts.",
      createdUser: "Jane Smith",
    },
    {
      title: "Quiz 2: Themes and Plugins",
      overview: "A quiz on using and managing themes and plugins.",
      createdUser: "Jane Smith",
    },
  ]);

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Create New Course"
            breadcrumbItems={[{ label: "Create New Course" }]}
            theme="metronic"
          />
          <div className="flex gap-3">
            <Button label="Add" variant="outline" theme="metronic" width="150px" onClick={() => (setCreateModal(true))} />
            <Button label="Publish" theme="metronic" width="150px" />
          </div>
        </div>
        <ContentList title="Chapters" items={chapterData} setItems={setChapterData} />
        <ContentList title="Quizzes" items={quizData} setItems={setQuizData} />
        <ConditionalDisplay condition={createModal}>
          <Modal visible={createModal} theme="metronic" onHide={() => setCreateModal(false)} header="Add New Module/Assignment" style={{ background: "#fff", width: "40vw" }}>
            <div className="flex w-full">
              <div className={style.imageContainer}>
                <div className={style.boxYellow}>
                  <Image src={YellowPlus} alt="plus" />
                </div>
                <div className="">
                  <h2 className={clsx('text-xl my-2 font-semibold', style.fontText)}>
                    New Module
                  </h2>
                  <span
                    className={clsx(style.textBlue, 'text-center')}>
                    Click and Upload
                  </span>
                </div>
              </div>
              <div className={style.imageContainer}>
                <div className={style.boxBlue}>
                  <Image src={BluePlus} alt="plus" className="z-1" />
                </div>
                <div className="">
                  <h2 className={clsx('text-xl my-2 font-semibold', style.fontText)}>New Assignment</h2>
                  <span
                    className={clsx(style.textBlue, 'text-center')}>
                    Click and Upload
                  </span>
                </div>
              </div>
            </div>
          </Modal>
        </ConditionalDisplay>
      </div>
    </PageContainer>
  );
}
