import Image from 'next/image'
import BreadCrumbs from '../../../components/UI/BreadCrumbs/BreadCrumbs'
import Backarrow from '../../../svg/metronic/back_metronic.svg'
import { PageContainer } from '../../../components/UI/Page/PageContainer/PageContainer'
import { Tab } from '../../../components/UI/Tabs/Tabs'
import CourseCard from '../../../components/LMS/CourseCard'
import style from '../index.module.css'
import TextInput from '../../../components/UI/Input/TextInput/TextInput'
import clsx from 'clsx'
import { useContext, useEffect } from 'react'
import { ConditionalDisplay } from '../../../components/UI/ConditionalDisplay/ConditionalDisplay'
import { LoadingScreen } from '../../../components/UI/LoadingScreen/LoadingScreen'
import { useCourseTabs } from '../../../hooks/LMS/AvailableCourses/useCourseTabs'
import { useAvailableCourses } from '../../../hooks/LMS/AvailableCourses/useAvailableCourses'
import Button from '../../../components/UI/Button/Button'
import { useRouter } from 'next/router'
import emptyCard from '../../../svg/metronic/empty_course_card.svg'
import empty from '../../../svg/metronic/empty_cat.svg'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'

export default function AvailableCourses() {
  const router = useRouter()
  const { currentTab, setCurrentTab, tabs } = useCourseTabs()
  const { userID: userId } = useContext(UserProfileContext)
  const {
    courses,
    totalCount,
    loading,
    globalFilter,
    onGlobalFilterChange,
    fetchCourses,
    loadMoreCourses,
    lazyParams,
  } = useAvailableCourses()

  useEffect(() => {
    if (userId) {
      fetchCourses(currentTab, userId)
    }
  }, [currentTab, lazyParams, userId])

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image src={Backarrow} alt="Back" />
          <BreadCrumbs
            title="Available Courses"
            breadcrumbItems={[
              { label: 'Available Courses' },
              { label: tabs.find((tab) => tab.value === currentTab)?.title },
            ]}
            theme="metronic"
          />
          <span
            className="p-input-icon-left mr-1"
            style={{
              display: 'flex',
              width: '22vw',
            }}
          >
            <i
              className={clsx('pi pi-search', style.searchIcon)}
              style={{ top: '22px' }}
            />
            <TextInput
              theme="metronic"
              placeholder="Search Course"
              value={globalFilter}
              onChange={onGlobalFilterChange}
            />
          </span>
        </div>

        {/* Tabs */}
        <div className={style.tabContainer}>
          <div className="flex gap-4" style={{ height: '2.5rem' }}>
            {tabs.map((tab) => (
              <Tab
                key={tab.value}
                title={tab.title}
                value={tab.value}
                display={true}
                isActive={currentTab === tab.value}
                handleClick={(e, value) => setCurrentTab(value)}
                theme="metronic"
              />
            ))}
          </div>
        </div>

        {/* Content */}
        <div style={{ width: '100%' }}>
          <ConditionalDisplay condition={loading && totalCount < 9}>
            <LoadingScreen />
          </ConditionalDisplay>

          <ConditionalDisplay condition={!loading || totalCount > 8}>
            <ConditionalDisplay condition={courses?.length === 0}>
              <div
                className="flex justify-content-center align-items-center"
                style={{ height: '30rem' }}
              >
                <Image src={empty} alt="No Courses Available" />
              </div>
            </ConditionalDisplay>
            {/* Course grid */}
            <ConditionalDisplay condition={courses?.length !== 0}>
              <div className="flex flex-wrap gap-4 justify-content-between">
                {courses?.map((course, index) => (
                  <CourseCard
                    key={`course-${index}`}
                    courseTitle={course?.title || 'Course Title'}
                    courseDescription={course?.createdUser || 'User'}
                    assignedDate={
                      course?.overview || `This is a ${course?.title} course`
                    }
                    buttonAction={() =>
                      router.push(`/LMS/AvailableCourses/${course?.id}`)
                    }
                    thumbnailUrl={course?.thumbnailUrl}
                    hasLeadEnrolled={course?.hasLeadEnrolled}
                  />
                ))}

                {/* Placeholder cards to fill the row */}
                {Array.from({ length: (4 - (courses?.length % 4)) % 4 }).map(
                  (_, index) => (
                    <div
                      key={`placeholder-${index}`}
                      className={clsx(
                        'flex justify-content-center align-items-center',
                        style.card
                      )}
                    >
                      <Image src={emptyCard} alt="Empty Course Placeholder" />
                    </div>
                  )
                )}
              </div>

              {/* Load more button */}
              <ConditionalDisplay condition={courses?.length < totalCount}>
                <div className="flex justify-content-center mt-5">
                  <Button
                    onClick={loadMoreCourses}
                    disabled={loading}
                    theme="metronic"
                    variant="outline"
                    label={loading ? 'Loading...' : 'Load More Courses'}
                  />
                </div>
              </ConditionalDisplay>
            </ConditionalDisplay>
          </ConditionalDisplay>
        </div>
      </div>
    </PageContainer>
  )
}
