import { useEffect, useState, useRef } from 'react'
import { useAccount, useMsal, useMsalAuthentication } from '@azure/msal-react'
import { useDashboard } from '../../../../hooks/useDashboard'
import { useApi } from '../../../../hooks/useApi'
import { InteractionType } from '@azure/msal-browser'
import { formBuilderApiRequest } from '../../../../src/msalConfig'
import useUtilityFunctions from '../../../../hooks/useUtilityFunctions'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { InputText } from 'primereact/inputtext'
import { useRouter } from 'next/router'
import useRoles from '../../../../hooks/useRoles'
import FillOutNewFormDashboard from '../FillOutNewFormDashboard/FillOutNewFormDashboard'
import PrimaryButton from '../../PrimaryButton/PrimaryButton'
import TabsWithUnderlines from '../../Tabs/TabUnderline'
import styles from './MySubmissionDashboard.module.css'
import DynamicTiles from '../../TileTabs/DynamicTiles'
import { msalInstance } from '../../../../pages/_app'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

export const MySubmissionsDashboard = ({
  tileValues,
  setTileValues,
  tabMapper,
}) => {
  const router = useRouter()
  const { roles, checkRole } = useRoles()
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const { username } = accounts.length > 0 ? accounts[0] : {}

  /* Variables for tiles and tabs */
  const [activeTile, setActiveTile] = useState(1)
  const [dashboardActiveTabIndex, setDashboardActiveTabIndex] = useState(0)
  const [topMenuTabIndex, setTopMenuTabIndex] = useState(0)
  const [apiCallUrl, setApiCallUrl] = useState(
    `${api}FormSubmission/filter/status/2`
  )
  const FIRST_TILE = 0
  const SECOND_TILE = 1
  const THIRD_TILE = 2
  /* ^Variables for tiles and tabs^ */

  const [isButtonClicked, setIsButtonClicked] = useState(false)
  const [showFillOutNewForm, setShowFillOutNewForm] = useState(false)
  const ref = useRef(null)
  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    selectedRow,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({})
  const { createDashboardDate } = useUtilityFunctions()
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest,
    msalInstance.getActiveAccount()
  )

  const { loading, callApi } = useApi()

  console.log('rows = ', rows)
  // Below is for initializing the dashboards
  const getDashboards = async () => {
    if (!account) return

    const { accessToken } = await acquireToken()

    const createApiParams = (status) => {
      const headersObj = {
        Accept: '*/*',
        Authorization: `Bearer ${accessToken}`,
      }

      if (typeof status === 'string') {
        return {
          method: 'GET',
          url: `${api}Form/${status}/${username}`,
          headers: headersObj,
        }
      }

      if (typeof status === 'number') {
        return {
          method: 'GET',
          url: `${api}FormSubmission/filter/status/${status}`,
          headers: headersObj,
        }
      }
    }

    const allFormSubmissionsParams = [
      { status: 'InProgress', params: createApiParams(2) },
      { status: 'Approved', params: createApiParams(6) },
      { status: 'Rejected', params: createApiParams(0) },
      { status: 'Drafts', params: createApiParams(11) },
    ]

    const userFormSubmissionsParams = [
      { status: 'InProgress', params: createApiParams('Pending') },
      { status: 'Approved', params: createApiParams('Approved') },
      { status: 'Rejected', params: createApiParams('Rejected') },
    ]

    const allFormSubmissionsApiCalls = allFormSubmissionsParams.map(
      async ({ status, params }) => {
        const response = await callApi(params)
        return { status, count: response?.data?.count }
      }
    )

    const userFormSubmissionsApiCalls = userFormSubmissionsParams.map(
      async ({ status, params }) => {
        const response = await callApi(params)
        return { status, count: response?.data?.count }
      }
    )

    let countsParameters
    if (roles && checkRole(['Admin', 'Viewer'])) {
      if (topMenuTabIndex === 0) {
        countsParameters = allFormSubmissionsApiCalls
      }
      if (topMenuTabIndex === 1) {
        countsParameters = userFormSubmissionsApiCalls
      }
    } else {
      countsParameters = userFormSubmissionsApiCalls
    }

    const counts = await Promise.all(countsParameters)

    const tileValues = counts.reduce((acc, { status, count }) => {
      return { ...acc, [`${status.toLowerCase()}Count`]: count }
    }, {})

    if (!tileValues.draftsCount) {
      setTileValues({
        ...tileValues,
        draftsCount: 0,
        recalledCount: 0,
      })
    } else {
      setTileValues({
        ...tileValues,
        recalledCount: 0,
      })
    }
  }

  useEffect(() => {
    if (account) {
      getDashboards()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [topMenuTabIndex, roles])
  // Above is for initializing the dashboards
  // We can use getAdminDashboard API to fill those values

  // Belows are for the tabs
  const topMenuTabs = ['All Submissions', 'My Submissions']
  const dashboardPendingTabs = [
    `Drafts (${tileValues.draftsCount})`,
    `Recalled (${tileValues.recalledCount})`,
  ]
  const dashboardInProgressTabs = [
    `In Review (${tileValues.inprogressCount})`,
    `On Hold (0)`,
  ]
  const dashboardFinalizedTabs = [
    `Approved (${tileValues.approvedCount})`,
    `Rejected (${tileValues.rejectedCount})`,
    'Cancelled (0)',
  ]
  let dashboardTabs

  const handleTabChange = (index) => {
    if (index === 0) {
      // All Submissions
      setApiCallUrl(`${api}FormSubmission/email/filter/status/2`)
      setActiveTile(1)
    }

    if (index === 1) {
      // My Submissions
      setApiCallUrl(`${api}FormSubmission/email/filter/status/2`)
      setActiveTile(1)
    }

    setTopMenuTabIndex(index)
  }

  switch (activeTile) {
    case 0:
      dashboardTabs = dashboardPendingTabs
      break
    case 1:
      dashboardTabs = dashboardInProgressTabs
      break
    case 2:
      dashboardTabs = dashboardFinalizedTabs
      break
    default:
      dashboardTabs = []
      break
  }
  // Above are for the tabs
  // Below is for changing the data in the dashboard
  const handleRowClick = (value) => {
    router.push(`/view/${value.formDefinition.id}/form-data/${value.id}`)
  }

  let loadLazyTimeout = null
  useEffect(() => {
    const loadLazyData = async () => {
      if (loadLazyTimeout) {
        clearTimeout(loadLazyTimeout)
      }

      const { accessToken } = await acquireToken()

      const queryString = lazyParamsToQueryString(lazyParams)

      const formSubmissionParams = {
        method: 'GET',
        url: apiCallUrl + queryString,
        headers: {
          Accept: '*/*',
          Authorization: `Bearer ${accessToken}`,
        },
        data: {
          query: queryString,
        },
      }

      const result = await callApi(formSubmissionParams)
      setRows(result?.data?.formSubmissions)
      setTotalCount(result?.data?.count)
    }

    if (account) {
      loadLazyData()
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [lazyParams, loadLazyTimeout, account, apiCallUrl])
  // Above is for changing the data in the dashboard

  // Below are for the tiles
  const tilesSubmission = [
    {
      title: 'Pending',
      number: tileValues.draftsCount + tileValues.recalledCount,
    },
    { title: 'In Progress', number: tileValues.inprogressCount },
    {
      title: 'Finalized',
      number: tileValues.approvedCount + tileValues.rejectedCount,
    },
  ]

  const handleTileClickSubmission = (index) => {
    let tempStatus

    if (topMenuTabIndex === 0) {
      if (index === FIRST_TILE) {
        // Pending (Drafts* + Recalled*)
        tempStatus = `${api}FormSubmission/email/filter/status/11`
      }

      if (index === SECOND_TILE) {
        // In Progress (In Review + On Hold*)
        tempStatus = `${api}FormSubmission/email/filter/status/2`
      }

      if (index === THIRD_TILE) {
        // Finalized (Approved + Rejected* + Cancelled*)
        tempStatus = `${api}FormSubmission/email/filter/status/6`
      }
    }

    // *These tabs still need backend functionality as of the time of this writing. 7/26/23

    if (topMenuTabIndex === 1) {
      // Pending (Drafts* + Recalled*)
      if (index === FIRST_TILE) {
        tempStatus = ''
      }

      if (index === SECOND_TILE) {
        // In Progress (In Review + On Hold*)
        tempStatus = `${api}FormSubmission/email/filter/2`
      }

      if (index === THIRD_TILE) {
        // Finalized (Approved + Rejected* + Cancelled*)
        tempStatus = `${api}FormSubmission/email/filter/3`
      }
    }

    setApiCallUrl(tempStatus)
    setActiveTile(index)
    setDashboardActiveTabIndex(0)
    setIsButtonClicked(false)
  }
  // Above are for the tiles

  // Below are for the dropdown buttons
  const handleClickOutside = (event) => {
    if (ref.current && !ref.current.contains(event.target)) {
      setIsButtonClicked(false)
    }
  }

  const handleButtonClick = (e) => {
    e.stopPropagation()
    setIsButtonClicked(!isButtonClicked)
  }

  const submitDocument = () => {
    router.push('/document/upload')
  }

  useEffect(() => {
    document.addEventListener('click', handleClickOutside)
    return () => {
      document.removeEventListener('click', handleClickOutside)
    }
  }, [])
  // Above are for the dropdown buttons

  // Below is for the DataTable
  const lastUpdatedBodyTemplate = (formSubmission) => {
    const { lastUpdatedAtUtc } = formSubmission
    return createDashboardDate(lastUpdatedAtUtc)
  }

  const submittedAtBodyTemplate = (formSubmission) => {
    const { submittedAtUtc } = formSubmission
    return createDashboardDate(submittedAtUtc)
  }

  console.log('Rows = ', rows)

  const renderHeader = () => {
    return (
      <>
        <FillOutNewFormDashboard
          isButtonClicked={showFillOutNewForm}
          setIsButtonClicked={setShowFillOutNewForm}
        />
      </>
    )
  }

  const headerStyle = { fontWeight: '600', fontSize: '15.5px', color: '#000' }

  return (
    <div className={styles.container}>
      <div className={styles.shadowContainer}>
        {/* <TabsWithUnderlines tabs={topMenuTabs} activeTabIndex={topMenuTabIndex} setActiveTabIndex={handleTabChange} /> */}
        <div className={styles.alignButton} ref={ref}>
          <PrimaryButton
            text="+ New"
            width="150"
            height="45"
            fontSize="15"
            onClick={handleButtonClick}
          />
          {isButtonClicked && (
            <div
              className="dropdown-menu"
              style={{
                display: 'flex',
                flexDirection: 'column',
                position: 'absolute',
                zIndex: '1000',
              }}
            >
              <PrimaryButton
                text="Submit Form"
                width="150"
                height="45"
                fontSize="15"
                loading={showFillOutNewForm}
                onClick={(e) => {
                  e.stopPropagation()
                  setShowFillOutNewForm((prev) => !prev)
                }}
              />
              <PrimaryButton
                text="Upload Document"
                width="150"
                height="45"
                fontSize="15"
                onClick={(e) => {
                  e.stopPropagation()
                  submitDocument()
                }}
              />
            </div>
          )}
        </div>
      </div>

      <DynamicTiles
        tiles={tilesSubmission}
        activeTile={activeTile}
        handleTileClick={handleTileClickSubmission}
        loading={loading}
      />

      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          margin: '20px 0',
        }}
      >
        {/* <TabsWithUnderlines tabs={dashboardTabs} activeTabIndex={dashboardActiveTabIndex} setActiveTabIndex={setDashboardActiveTabIndex} /> */}
        <div
          style={{
            display: 'flex',
            justifyContent: 'end',
            alignItems: 'center',
            marginLeft: '20px',
          }}
        >
          <span className="p-input-icon-right">
            <InputText
              className="p-inputtext-lg"
              style={{ width: '400px', height: '2.8rem' }}
              value={globalFilter}
              onChange={onGlobalFilterChange}
              placeholder="Search"
            />
            <i className="pi pi-search" style={{ fontSize: '1.3rem' }} />
          </span>
        </div>
      </div>

      <DataTable
        value={rows}
        lazy
        columnResizeMode="expand"
        dataKey="id"
        paginator
        first={lazyParams.first}
        rows={lazyParams.rows}
        onSort={onSort}
        onPage={onPage}
        onFilter={onFilter}
        sortField={lazyParams.sortField}
        sortOrder={lazyParams.sortOrder}
        totalRecords={totalCount}
        filters={lazyParams.filters}
        header={renderHeader}
        loading={loading}
        globalFilterFields={[]}
        selectionMode="single"
        onSelectionChange={(e) => handleRowClick(e.value)}
        className={styles.shadowContainerDataTable}
      >
        <Column
          className="dashboardTitle"
          field="formSubmissionId"
          header="Form ID"
          sortable
          headerStyle={{ ...headerStyle, width: '10%' }}
        />
        <Column
          className="dashboardTitle"
          field="formDefinition.name"
          header="Form Title"
          sortable
          headerStyle={{ ...headerStyle, width: '16%' }}
        />
        <Column
          className=""
          field="formDefinition.department.name"
          header="Department"
          sortable
          headerStyle={{ ...headerStyle, width: '14%' }}
        />
        <Column
          className=""
          field="formDefinition.description"
          header="Description"
          sortable
          headerStyle={{ ...headerStyle, width: '14%' }}
        />
        {/* <Column
          className="dashboardTitle"
          field="stageTitle"
          header="Stage"
          sortable
          headerStyle={{ ...headerStyle, width: "14%" }}
        /> */}
        <Column
          className="dashboardTitle"
          field="userFullLegalName"
          header="Initiated By"
          sortable
          headerStyle={{ ...headerStyle, width: '18%' }}
        />
        <Column
          className="dashboardTitle"
          field="submittedAtUtc"
          header="Date Submitted"
          body={submittedAtBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: '15%' }}
        />
        <Column
          className="dashboardTitle"
          field="lastUpdatedAtUtc"
          header="Last Updated"
          body={lastUpdatedBodyTemplate}
          sortable
          headerStyle={{ ...headerStyle, width: '25%' }}
        />
      </DataTable>
    </div>
  )
}
