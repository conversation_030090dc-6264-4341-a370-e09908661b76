import { useContext } from 'react'
import { useApi } from '../../useApi'
import { useDashboard } from '../../useDashboard'
import UserProfileContext from '../../../public/UserProfileContext/UserProfileContext'

export const useAvailableCourses = (rowCount = 8, hasPublished = true) => {
  const { callApi, loading } = useApi()
  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    onPage,
    onSort,
    lazyParamsToQueryString,
    globalFilter,
    onGlobalFilterChange,
  } = useDashboard({
    initialLazyParams: {
      first: 0,
      rows: rowCount,
      sortOrder: -1,
      page: 0,
      filters: {
        global: { value: '', matchMode: 'contains' },
      },
    },
  })

  const fetchCourses = async (currentTab, userId) => {
    const queryString =
      currentTab === 0
        ? lazyParamsToQueryString(lazyParams) +
          `&leadUserProfileId=${userId}&hasPublished=${hasPublished}`
        : lazyParamsToQueryString(lazyParams) +
          `&CourseTypeId=${
            currentTab + 1
          }&leadUserProfileId=${userId}&hasPublished=${hasPublished}`

    try {
      const response = await callApi({
        method: 'GET',
        url: `Courses/${queryString}`,
      })
      if (response?.data) {
        setRows(response.data.rows)
        setTotalCount(response.data.count)
      }
    } catch (error) {
      console.error('Error fetching form definition options:', error)
    }
  }

  const loadMoreCourses = () => {
    const newLazyParams = {
      ...lazyParams,
      rows: lazyParams.rows + 4,
    }
    onPage(newLazyParams)
  }

  return {
    courses: rows,
    totalCount,
    loading,
    globalFilter,
    onGlobalFilterChange,
    fetchCourses,
    loadMoreCourses,
    lazyParams,
    onPage,
    onSort,
  }
}
