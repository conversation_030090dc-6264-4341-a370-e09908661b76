import FormViewTabs from '../../../components/UI/FormViewTabs/FormViewTabs'
import FormSubmission from '../../../components/FormSubmission/FormSubmission'
import { useRouter } from 'next/router'

export default function View({ solutionId = 1 }) {
  const breadCrumbObj = {
    title: solutionId === 6 ? 'My Request' : 'My Submissions',
    breadcrumbItems: [
      { label: solutionId === 6 ? 'My Request' : 'My Submissions' },
    ],
  }

  return (
    <FormSubmission
      FormTabs={FormViewTabs}
      breadCrumbObj={breadCrumbObj}
      showDiscardButton={false}
      isConnectPage={false}
    />
  )
}
