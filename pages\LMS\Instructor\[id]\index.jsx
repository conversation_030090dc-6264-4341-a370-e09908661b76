import Image from 'next/image'
import { PageContainer } from '../../../../components/UI/Page/PageContainer/PageContainer'
import BreadCrumbs from '../../../../components/UI/BreadCrumbs/BreadCrumbs'
import Backarrow from '../../../../svg/metronic/back_metronic.svg'
import Plus from '../../../../svg/metronic/yellow_plus.svg'
import style from '../../../../pages/LMS/index.module.css'
import { FileUpload } from 'primereact/fileupload'
import { useRouter } from 'next/router'
import Button from '../../../../components/UI/Button/Button'

export default function CreateCourse() {
  const router = useRouter()

  return (
    <PageContainer theme="metronic">
      <div className="flex flex-column gap-5">
        <div className="flex gap-4 mt-1">
          <Image
            src={Backarrow}
            alt="Back"
            className="cursor-pointer"
            onClick={() => router.back()}
          />
          <BreadCrumbs
            title="Curate New Course"
            breadcrumbItems={[{ label: 'Curate New Course' }]}
            theme="metronic"
          />
          <Button label="Publish" theme="metronic" width="200px" />
        </div>
        <div className={style.pageCard}>
          <div className="flex flex-column justify-content-center align-items-center gap-3 py-5">
            <div className={style.imageBoxYellow}>
              <Image src={Plus} alt="static" />
            </div>
            <p>
              Click and add <strong>NEW MODULE</strong>
            </p>
            <FileUpload
              name="courseImage"
              mode="basic"
              accept="video/*,application/pdf"
              customUpload
              chooseOptions={{
                className: 'custom-choose-btn-secondary',
                icon: 'none',
                style: { width: '150px' },
              }}
              chooseLabel="Upload"
              uploadHandler={(e) => {
                console.log(e.files)
              }}
            />
          </div>
        </div>
      </div>
    </PageContainer>
  )
}
