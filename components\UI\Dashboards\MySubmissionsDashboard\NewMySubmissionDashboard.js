import { useEffect, useState } from 'react'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import { useDashboard } from '../../../../hooks/useDashboard'
import { useApi } from '../../../../hooks/useApi'
import { Tab, TabContainer } from '../../Tabs/Tabs'
import { useAccount, useMsal, useMsalAuthentication } from '@azure/msal-react'
import { InteractionType } from '@azure/msal-browser'
import { formBuilderApiRequest } from '../../../../src/msalConfig'
import { InputText } from 'primereact/inputtext'
import { useRouter } from 'next/router'
import useUtilityFunctions from '../../../../hooks/useUtilityFunctions'
import styles from './MySubmissionDashboard.module.css'
import { ApproverTimeLine } from '../../../../components/WorkflowBuilder/WorkflowNode/ApproverStatus/ApproverStatus'
import ProjectDashboard from './MySolutionSubmissions/ProjectDashboard'
import PurchaseDashboard from './MySolutionSubmissions/PurchaseDashboard'
import PerformanceDashboard from './MySolutionSubmissions/PerformanceDashboard'
import clsx from 'clsx'
import { ConditionalDisplay } from '../../ConditionalDisplay/ConditionalDisplay'
import MyRequestsDashboard from '../OnBoardingPageDashboard/MyRequestsDashboard/MyRequestsDashboard'
import { useGetFormSubmissionDashboard } from '../../../../hooks/useGetFormSubmissionDashboard'
import { PageElementContainer } from '../../Page/PageElementContainer/PageElementContainer'
import { descriptionBodyTemplate } from '../../../UI/Templates/BodyTemplates'
import SubmissionTabs from '../../Tabs/SubmissionTabs'

const api = process.env.NEXT_PUBLIC_FORM_BUILDER_API

const NewMySubmissionDashboard = ({
  tileTitleMapper,
  selectedTile,
  activeTab,
  handleTabClick,
  currentDashboard,
  allStatusCounts,
  userStatusCounts,
  solutionId = 1,
  username,
}) => {
  const headerStyle = { fontWeight: '600', fontSize: '15.5px', color: '#000' }
  const router = useRouter()
  const { accounts } = useMsal()
  const account = useAccount(accounts[0] ?? {})
  const { acquireToken } = useMsalAuthentication(
    InteractionType.Silent,
    formBuilderApiRequest
  )

  const initialLazyParams = {
    first: 0,
    page: 0,
    rows: 10,
    sortField: 'initiatedDate',
    sortOrder: -1,
    filters: {
      global: {
        value: '',
        matchMode: 'contains',
      },
    },
  }

  const {
    rows,
    setRows,
    totalCount,
    setTotalCount,
    lazyParams,
    globalFilter,
    lazyParamsToQueryString,
    onSort,
    onPage,
    onFilter,
    onGlobalFilterChange,
  } = useDashboard({ initialLazyParams })

  const handleRowClick = (value) => {
    if (solutionId === 6) {
      console.log('value', value)
      return router.push(
        `/PurchaseOrder/view/${value.formDefinition.id}/myRequest/${value.id}`
      )
    } else if (solutionId === 4) {
      console.log('value 1', value)
      return router.push(
        `/Supplier/view/${value.formDefinition.id}/mySubmissions/${value.id}`
      )
    }
    if (value?.status === 11) {
      console.log('value 2', value)
      // 11 is the status code for Drafts
      router.push(`/view/${value.formDefinition.id}/drafts/${value.id}`)
    } else {
      console.log('value 3', value)
      router.push(`/view/${value.formDefinition.id}/mySubmissions/${value.id}`)
    }
  }

  const { formSubmissionData, loading } = useGetFormSubmissionDashboard({
    accounts,
    acquireToken,
    lazyParams,
    lazyParamsToQueryString,
    activeTab,
    currentDashboard,
    solutionId,
  })

  useEffect(() => {
    setRows(formSubmissionData?.formSubmissions)
    setTotalCount(formSubmissionData?.count)
  }, [
    lazyParams,
    acquireToken,
    account,
    activeTab,
    currentDashboard,
    formSubmissionData,
  ])

  /* 
        I used the logic below for the title attribute in the Tab components because we need the tabs to populate with the
        counts for each status i.e. Drafts, Recalled, etc. but we also need to make sure that if the count variable(s) is 
        undefined then we'll use a zero numeric value. Otherwise the tabs will look like the following if the count
        variable(s) is undefined: Drafts (undefined)
    */

  const rowExpansionTemplate = (data) => {
    return (
      <div className="p-3">
        <h3>Purchase Timeline</h3>
        <ApproverTimeLine events={events} />
      </div>
    )
  }

  const displayWaitingOn = (rowData) => {
    const inReviewAuthors = [
      ...new Set(
        rowData.formTransactions
          .filter((transaction) => transaction.statusText === 'InReview')
          .map((transaction) => transaction.authorLegalName)
      ),
    ].join(', ')

    return inReviewAuthors
  }

  return (
    <>
      <div className={styles.subTab}>
        <div className={styles.tabBack}>
          <TabContainer>
            <SubmissionTabs
              currentDashboard={currentDashboard}
              userStatusCounts={userStatusCounts}
              allStatusCounts={allStatusCounts}
              activeTab={activeTab}
              handleTabClick={handleTabClick}
              selectedTile={selectedTile}
              tileTitleMapper={tileTitleMapper}
              solutionId={solutionId}
            />
            {solutionId === 2 && (
              <>
                <Tab
                  // icon="pi pi-clock"
                  title={`Waiting For Me (${
                    allStatusCounts?.pendingCount ?? 0
                  })`}
                  value={11}
                  isActive={activeTab === 11}
                  handleClick={handleTabClick}
                  display={true}
                />
                <Tab
                  // icon="pi pi-check"
                  title={`On Going Reviews (${
                    allStatusCounts?.inProgressCount ?? 0
                  })`}
                  value={2}
                  isActive={activeTab === 2}
                  handleClick={handleTabClick}
                  display={true}
                />
                <Tab
                  // icon="pi pi-check"
                  title={`Completed Reviews (${
                    allStatusCounts?.finalizedCount ?? 0
                  })`}
                  value={3}
                  isActive={activeTab === 3}
                  handleClick={handleTabClick}
                  display={true}
                />
              </>
            )}
          </TabContainer>
        </div>
        <div
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            alignItems: 'center',
            marginLeft: 'auto',
          }}
        >
          <span className="p-input-icon-right">
            <InputText
              className={clsx('p-inputtext-lg', styles.search)}
              value={globalFilter}
              onChange={onGlobalFilterChange}
              placeholder="Search"
            />
            <i className={clsx('pi pi-search', styles.icon)} />
          </span>
        </div>
      </div>
      {(solutionId === 1 || solutionId === 7 || solutionId === 9) && (
        <PageElementContainer className={styles.submissionTable}>
          <DataTable
            value={rows}
            lazy
            columnResizeMode="expand"
            dataKey="id"
            paginator
            first={lazyParams.first}
            rows={lazyParams.rows}
            onSort={onSort}
            onPage={onPage}
            onFilter={onFilter}
            sortField={lazyParams.sortField}
            sortOrder={lazyParams.sortOrder}
            totalRecords={totalCount}
            filters={lazyParams.filters}
            loading={loading}
            globalFilterFields={[]}
            selectionMode="single"
            onSelectionChange={(e) => handleRowClick(e.value)}
          >
            <Column
              className="dashboardTitle"
              field="formSubmissionId"
              header="Form ID"
              sortable
              headerStyle={{ ...headerStyle, width: '7%' }}
            />
            <Column
              className="dashboardTitle"
              field="formDefinition.name"
              header="Form Title"
              sortable
              headerStyle={{ ...headerStyle, width: '10%' }}
            />
            <Column
              className=""
              field="formDefinition.formDefinitionMaster.department.name"
              header="Department"
              sortable
              headerStyle={{ ...headerStyle, width: '10%' }}
            />
            <Column
              className=""
              field="description"
              header="Description"
              sortable
              headerStyle={{ ...headerStyle, width: '10%' }}
              body={descriptionBodyTemplate}
            />
            {selectedTile === 'In Progress' ? (
              <Column
                className="dashboardTitle"
                field="stageTitle"
                header="Current Stage"
                sortable
                headerStyle={{ ...headerStyle, width: '10%' }}
              />
            ) : null}
            {selectedTile === 'In Progress' ? (
              <Column
                className="dashboardTitle"
                field="currentApproverFullLegalName"
                body={(data) =>
                  data.status === 3 || data.status === 4
                    ? 'NA'
                    : data.currentApproverGroup > 0
                      ? data.currentApproverGroupName
                      : data.currentApproverFullLegalName
                }
                header="Waiting On"
                sortable
                headerStyle={{ ...headerStyle, width: '10%' }}
              />
            ) : null}
            <Column
              className="dashboardTitle"
              field="userFullLegalName"
              header="Initiated By"
              headerStyle={{ ...headerStyle, width: '10%' }}
            />
            <Column
              className="dashboardTitle"
              field="submittedAtUtc"
              header="Date Submitted"
              body={SubmittedAtBodyTemplate}
              sortable
              headerStyle={{ ...headerStyle, width: '11%' }}
            />
            <Column
              className="dashboardTitle"
              field="lastUpdatedAtUtc"
              header="Last Updated"
              body={LastUpdatedBodyTemplate}
              sortable
              headerStyle={{ ...headerStyle, width: '10%' }}
            />
          </DataTable>
        </PageElementContainer>
      )}
      {solutionId === 2 && (
        <PerformanceDashboard
          activeTab={activeTab}
          username={username}
          rows={rows}
          lazyParams={lazyParams}
          onSort={onSort}
          onPage={onPage}
          onFilter={onFilter}
          totalCount={totalCount}
          loading={loading}
          globalFilterFields={[]}
          handleRowClick={handleRowClick}
          headerStyle={headerStyle}
          submittedAtBodyTemplate={SubmittedAtBodyTemplate}
          lastUpdatedBodyTemplate={LastUpdatedBodyTemplate}
        />
      )}
      {solutionId === 5 && (
        <ProjectDashboard
          rows={rows}
          lazyParams={lazyParams}
          onSort={onSort}
          onPage={onPage}
          onFilter={onFilter}
          totalCount={totalCount}
          loading={loading}
          globalFilterFields={[]}
          handleRowClick={handleRowClick}
          headerStyle={headerStyle}
          submittedAtBodyTemplate={SubmittedAtBodyTemplate}
          lastUpdatedBodyTemplate={LastUpdatedBodyTemplate}
        />
      )}
      {solutionId === 6 && (
        <PurchaseDashboard
          rows={rows}
          lazyParams={lazyParams}
          onSort={onSort}
          onPage={onPage}
          onFilter={onFilter}
          totalCount={totalCount}
          loading={loading}
          globalFilterFields={[]}
          handleRowClick={handleRowClick}
          headerStyle={headerStyle}
          submittedAtBodyTemplate={SubmittedAtBodyTemplate}
          lastUpdatedBodyTemplate={LastUpdatedBodyTemplate}
          selectedTile={selectedTile}
          solutionId={6}
        />
      )}
      {solutionId === 4 && (
        <PurchaseDashboard
          rows={rows}
          lazyParams={lazyParams}
          onSort={onSort}
          onPage={onPage}
          onFilter={onFilter}
          totalCount={totalCount}
          loading={loading}
          globalFilterFields={[]}
          handleRowClick={handleRowClick}
          headerStyle={headerStyle}
          submittedAtBodyTemplate={SubmittedAtBodyTemplate}
          lastUpdatedBodyTemplate={LastUpdatedBodyTemplate}
          selectedTile={selectedTile}
          solutionId={4}
        />
      )}
      <ConditionalDisplay condition={solutionId === 3}>
        <MyRequestsDashboard
          rows={rows}
          lazyParams={lazyParams}
          onSort={onSort}
          onPage={onPage}
          onFilter={onFilter}
          totalCount={totalCount}
          loading={loading}
          globalFilter={globalFilter}
          onGlobalFilterChange={onGlobalFilterChange}
          headerStyle={headerStyle}
          submittedAtBodyTemplate={SubmittedAtBodyTemplate}
          lastUpdatedBodyTemplate={LastUpdatedBodyTemplate}
          selectedTile={selectedTile}
          solutionId={3}
        />
      </ConditionalDisplay>
    </>
  )
}

export default NewMySubmissionDashboard

export function SubmittedAtBodyTemplate(formSubmission) {
  const { createDashboardDate } = useUtilityFunctions()
  const { submittedAtUtc } = formSubmission
  return createDashboardDate(submittedAtUtc)
}

export function LastUpdatedBodyTemplate(formSubmission) {
  const { createDashboardDate } = useUtilityFunctions()
  const { lastUpdatedAtUtc } = formSubmission
  return createDashboardDate(lastUpdatedAtUtc)
}
