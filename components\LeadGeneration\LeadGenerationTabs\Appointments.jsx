import { InputText } from 'primereact/inputtext'
import Button from '../../UI/Button/Button'
import { ConditionalDisplay } from '../../UI/ConditionalDisplay/ConditionalDisplay'
import { DataTable } from 'primereact/datatable'
import { Column } from 'primereact/column'
import {
  Suspense,
  useEffect,
  lazy,
  forwardRef,
  useState,
  useCallback,
} from 'react'
import rightarrow from '../../../svg/metronic/rightArrow.svg'
import leftarrow from '../../../svg/metronic/leftArrow.svg'
import Image from 'next/image'
import styles from '../../../pages/LeadGeneration/index.module.css'
import clsx from 'clsx'
import { useCalendarInstance } from '../../../hooks/LeadGeneration/useCalendarInstance'

const Calendar = lazy(() => import('@toast-ui/react-calendar'))
const calendars = [
  {
    id: 'cal1',
    name: 'Work',
    backgroundColor: '#EFF6FF',
    borderColor: '#1B84FF',
    // borderColor: "none",

    radius: '20px',
    isAllDay: false,
    category: 'time',
    customStyle: {
      fontSize: '40px',
      fontWeight: 'bold',
      borderRadius: '20px',
    },
  },
]

const onAfterRenderEvent = (event) => {
  //console.log("title", event.title);
}
export const Appointments = forwardRef(
  (
    {
      calendarTimeline,
      calendarView,
      assignee,
      template,
      openModal,
      getFieldsFromColumns,
    },
    ref
  ) => {
    const [isClient, setIsClient] = useState(false)
    const { calendarRef, saveCurrentView, restoreView } = useCalendarInstance()

    useEffect(() => {
      if (typeof window !== 'undefined') {
        setIsClient(true)
      }
    }, [])

    const handlePrev = () => {
      // dayCount = dayCount - 1;
      const calendarInstance = ref?.current?.getInstance()
      calendarInstance.prev()
    }

    const handleNext = () => {
      const instance = ref?.current?.getInstance()
      if (instance) {
        instance.next()
      }
    }

    const handleToday = () => {
      const calendarInstance = ref?.current?.getInstance()
      calendarInstance?.today()
    }

    const handleOpenModal = useCallback(() => {
      saveCurrentView()
      openModal(
        getFieldsFromColumns(
          mockColumns
            .filter(
              (col) => col.field !== 'endTime' && col.field !== 'bookingStatus'
            )
            .concat({ field: 'leadName', header: 'Lead Name' })
        ),
        'Create New Appointment',
        'appointment'
      )
    }, [openModal, getFieldsFromColumns, saveCurrentView])

    useEffect(() => {
      // Restore view when modal closes
      restoreView()
    }, [restoreView])

    const mockRows = [
      {
        action: 'ViewEdit',
        title: 'Course Update',
        type: 'Phone',
        assignee: assignee,
        startTime: '05/16/2025 09:00AM PT',
        endTime: '05/16/2025 09:45AM PT',
        bookingStatus: 'Scheduled',
        description: 'Discussion on the course updates in phone',
      },
      {
        action: 'ViewEdit',
        title: 'Course Review',
        type: 'Teams Meeting',
        assignee: assignee,
        startTime: '05/17/2025 02:30PM PT',
        endTime: '05/17/2025 03:30PM PT',
        bookingStatus: 'Confirmed',
        description: 'Shared the course details',
      },
      {
        action: 'ViewEdit',
        title: 'Follow Up',
        type: 'Teams Meeting',
        assignee: assignee,
        startTime: '05/18/2025 11:15AM PT',
        endTime: '05/18/2025 12:00PM PT',
        bookingStatus: 'Cancelled',
        description: 'Shared the technical requirements for the course',
      },
      {
        action: 'ViewEdit',
        title: 'Enrollment Discussion Session',
        type: 'In Person',
        assignee: assignee,
        startTime: '05/20/2025 04:00PM PT',
        endTime: '05/20/2025 05:00PM PT',
        bookingStatus: 'Cancelled',
        description:
          'Discussed the final updates and shared the information on the enrollment forms',
      },
      // ...Array(7).fill({
      //   action: "ViewEdit",
      //   title: "$name",
      //   type: "$Type",
      //   assignee: "$assignee",
      //   startTime: "$start_date_time",
      //   endTime: "$end_date_time",
      //   bookingStatus: "$status",
      // }),
    ]

    const actionBodyTemplate = () => (
      <span style={{ display: 'flex', gap: '0.5rem', alignItems: 'center' }}>
        <span
          className="pi pi-eye"
          style={{ fontSize: '1.2rem', cursor: 'pointer' }}
          onClick={() => {}}
        />
        <span
          className="pi pi-pencil"
          style={{ fontSize: '1.2rem', cursor: 'pointer' }}
          onClick={() => {}}
        />
      </span>
    )
    const tableheader = () => (
      <div className={styles.prospecTable}>
        <span className="text-xl font-bold">Appointments</span>
        <div className="flex gap-4">
          <span
            className="p-input-icon-left"
            style={{
              display: 'flex',
              flexDirection: 'row-reverse',
            }}
          >
            <i
              className={clsx('pi pi-search', styles.searchIcon)}
              style={{ top: '22px' }}
            />
            <InputText
              name="appointmentsSearch"
              placeholder="Search"
              className={styles.search}
            />
          </span>
          <Button
            // onClick={() =>
            //   openModal(
            //     getFieldsFromColumns(
            //       mockColumns
            //         .filter(
            //           (col) =>
            //             col.field !== 'endTime' && col.field !== 'bookingStatus'
            //         )
            //         .concat({ field: 'leadName', header: 'Lead Name' })
            //     ),
            //     'Create New Appointment',
            //     'appointment'
            //   )
            // }
            onClick={handleOpenModal}
            label={'Create New Appoinment'}
            variant={'fill'}
            theme="metronic"
          />
        </div>
      </div>
    )

    const mockColumns = [
      {
        field: 'action',
        header: 'Action',
        body: actionBodyTemplate,
      },
      { field: 'title', header: 'Title' },
      { field: 'type', header: 'Type' },
      { field: 'description', header: 'Description' },
      { field: 'startTime', header: 'Start Time' },
      { field: 'assignee', header: 'Assignee' },
      { field: 'endTime', header: 'End Time' },
      { field: 'bookingStatus', header: 'Booking Status' },
    ]

    return (
      <div>
        <div className="mt-4" />
        <div className="w-full flex justify-content-between"></div>
        <ConditionalDisplay condition={!calendarView}>
          <DataTable
            header={tableheader}
            className="custom-lead"
            value={mockRows}
            paginator
            rows={10}
            style={{ cursor: 'pointer' }}
          >
            {mockColumns.map((col, index) => (
              <Column
                key={index}
                field={col.field}
                header={col.header}
                {...(col.body ? { body: col.body } : {})}
              />
            ))}
          </DataTable>
        </ConditionalDisplay>
        <ConditionalDisplay condition={calendarView}>
          <div
            className="flex flex-column justify-content-center align-items-center"
            style={{
              backgroundColor: '#F1F7FD',
              border: '1px solid #5151511A',
              borderRadius: '10px',
            }}
          >
            <div
              className="w-full h-100 flex justify-content-between align-items-center p-3"
              style={{ backgroundColor: '#F1F7FD' }}
            >
              <div className="flex align-items-center gap-3">
                <Button
                  iconOnly
                  icon={<Image src={rightarrow} />}
                  onClick={handlePrev}
                  // label={"<<<"}
                  // variant="outline"
                  // style={{ marginRight: "10px" }}
                />
                <span
                  onClick={handleToday}
                  label={'Today'}
                  style={{
                    color: '#1B84FF',
                    fontSize: '14px',
                    fontWeight: '700',
                    cursor: 'pointer',
                  }}
                  // variant="outline"
                >
                  Today
                </span>
                <Button
                  iconOnly
                  icon={<Image src={leftarrow} />}
                  onClick={handleNext}
                  // label={">>>"}
                  // // theme="metronic"
                  // // variant="outline"
                  // style={{ marginLeft: "10px" }}
                />
              </div>
              <Button
                onClick={() =>
                  openModal(
                    getFieldsFromColumns(
                      mockColumns
                        .filter(
                          (col) =>
                            col.field !== 'endTime' &&
                            col.field !== 'bookingStatus'
                        )
                        .concat({ field: 'leadName', header: 'Lead Name' })
                    ),
                    'Create New Appointment',
                    'appointment'
                  )
                }
                label={'Create New Appoinment'}
                variant={'fill'}
                theme="metronic"
              />
            </div>
            {console.log('calT', calendarTimeline)}
            <ConditionalDisplay condition={isClient}>
              <Suspense fallback={<div>Loading calendar...</div>}>
                <div style={{ width: '100%' }}>
                  <Calendar
                    usageStatistics={false}
                    ref={ref}
                    height="800px"
                    view="week"
                    week={{
                      eventView: ['time'],
                      taskView: false,
                      startDayOfWeek: 0,
                      hourStart: 8,
                      hourEnd: 21,
                      hourHeight: 60,
                    }}
                    template={template}
                    scheduleView={['time']}
                    month={{
                      dayNames: ['M', 'T', 'W', 'T', 'F', 'S'],
                      visibleWeeksCount: 3,
                    }}
                    calendars={calendars}
                    events={calendarTimeline}
                    onAfterRenderEvent={onAfterRenderEvent}
                    theme={{
                      theme: {
                        common: {
                          backgroundColor: '#fff',
                          holiday: {
                            color: '#f00',
                          },
                          today: {
                            color: '#00aaff',
                          },
                        },
                        week: {
                          dayName: {
                            backgroundColor: '#eee',
                            color: '#222',
                            height: '50px',
                          },
                          timeGridSchedule: {
                            backgroundColor: '#bada55',
                            borderRadius: '8px',
                          },
                          nowIndicatorPast: {
                            border: '1px dashed red',
                          },
                        },
                        month: {
                          schedule: {
                            backgroundColor: '#ffd700',
                          },
                        },
                      },
                    }}
                  />
                </div>
              </Suspense>
            </ConditionalDisplay>
          </div>
        </ConditionalDisplay>
      </div>
    )
  }
)

Appointments.displayName = 'Appointments'
